# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Common Development Commands

### Development Server
```bash
pnpm dev
```
Starts the Next.js development server on `http://localhost:3000`

### Build and Production
```bash
pnpm build  # Build the production application
pnpm start  # Start the production server
```

### Code Quality
```bash
pnpm lint   # Run ESLint to check code quality
```

## Project Architecture

### Core Technology Stack
- **Framework**: Next.js 15 with App Router
- **Language**: TypeScript with strict configuration
- **Styling**: Tailwind CSS v4 with custom design system
- **UI Components**: Radix UI primitives with shadcn/ui components
- **AI Integration**: OpenAI GPT, Google Gemini, and Crayon AI React libraries
- **Voice**: Vapi.ai for voice assistant capabilities
- **Analytics**: PostHog for user tracking

### Application Structure

This is a healthcare virtual Assistant application with the following key areas:

#### **Core Chat System** (`src/contexts/ChatContext.tsx`)
- Thread-based conversation management with persistent storage
- Multi-modal message support (text, function calls, system events)
- Voice call event tracking and transcript integration
- Dynamic "For You" item management for personalized suggestions

#### **Page Architecture**
- **Main Pages**: Home (`/`), Care Canvas (`/care-canvas`), Profile (`/profile`), Journeys (`/journeys`)
- **Internal Tools**: Clinical outreach, Crohn's diagnosis, prior auth steerage, login pages
- **Omnichannel**: Customer service interface with mock data

#### **AI Assistant Integration**
- Multiple AI providers: OpenAI, Gemini, and Crayon AI
- Function calling capabilities for healthcare-specific actions
- Voice assistant modal integration with Vapi.ai
- Outbound call API for proactive patient engagement

#### **Healthcare Data Types** (`src/types/index.ts`)
Comprehensive type definitions for:
- Patient profiles and medical conditions
- Insurance plans and claims data
- Prescriptions and pharmacy preferences
- Care team members and providers
- Health records and plan usage

#### **Component Organization**
- **UI Components**: shadcn/ui based design system in `src/components/ui/`
- **Chat Components**: Conversation UI, suggestions, appointments, prescriptions
- **Profile Components**: Medical conditions, care team, claims, prescriptions
- **Care Canvas**: Interactive healthcare journey visualization
- **Animation**: Custom scroll-based animations and transitions

### Key Features

#### **Multi-Provider AI Chat**
The application supports multiple AI providers with function calling:
- OpenAI GPT for general conversations
- Google Gemini for healthcare-specific queries
- Crayon AI for structured healthcare workflows

#### **Voice Assistant Integration**
- Vapi.ai integration for voice conversations
- Voice call event tracking in chat threads
- Transcript selection and integration

#### **Healthcare Data Management**
- Comprehensive patient profile system
- Insurance claim processing and tracking
- Prescription management with pharmacy integration
- Care team coordination and provider search

#### **Responsive Design**
- Mobile-first approach with responsive breakpoints
- Custom hooks for responsive behavior (`use-mobile.ts`, `use-responsive.ts`)
- Tailwind CSS with custom healthcare-themed design system

### Development Patterns

#### **State Management**
- React Context for global chat state
- Local storage persistence for chat threads and user preferences
- Custom hooks for specific data domains (profile, transcripts, appointments)

#### **Component Patterns**
- Functional components with TypeScript
- Custom hooks for complex logic
- Radix UI primitives for accessibility
- Consistent prop interfaces and error handling

#### **API Structure**
- Next.js API routes for AI assistant endpoints
- RESTful pattern for healthcare data operations
- Environment-based configuration for AI providers

### File Organization Notes

- **Aliases**: Uses `@/` prefix for all imports (configured in `tsconfig.json`)
- **shadcn/ui**: Configured with "new-york" style and Lucide icons
- **Tailwind**: Custom CSS variables for theming with dark mode support
- **Assets**: Images stored in `public/images/` with organized subdirectories

### Testing and Quality

- TypeScript strict mode enabled
- ESLint configuration for code quality
- PostHog integration for user analytics and error tracking
- Toast notifications using Sonner for user feedback