{"type": "inboundPhoneCall", "costs": [{"type": "transport", "provider": "twi<PERSON>", "minutes": 42, "cost": 42}], "messages": [{"role": "foo", "message": "foo", "time": 42, "endTime": 42, "secondsFromStart": 42, "duration": 42}], "phoneCallProvider": "twi<PERSON>", "phoneCallTransport": "sip", "status": "scheduled", "endedReason": "call-start-error-neither-assistant-nor-server-set", "destination": {"message": "foo", "type": "number", "numberE164CheckEnabled": true, "number": "foo", "extension": "foo", "callerId": "foo", "transferPlan": {"mode": "blind-transfer", "message": "foo", "timeout": 60, "sipVerb": "refer", "holdAudioUrl": "foo", "transferCompleteAudioUrl": "foo", "twiml": "foo", "summaryPlan": {"messages": [{}], "enabled": true, "timeoutSeconds": 42}, "sipHeadersInReferToEnabled": true, "fallbackPlan": {"message": "foo", "endCallEnabled": true}}, "description": "foo"}, "id": "foo", "orgId": "foo", "createdAt": "foo", "updatedAt": "foo", "startedAt": "foo", "endedAt": "foo", "cost": 42, "costBreakdown": {"transport": 42, "stt": 42, "llm": 42, "tts": 42, "vapi": 42, "chat": 42, "total": 42, "llmPromptTokens": 42, "llmCompletionTokens": 42, "ttsCharacters": 42, "analysisCostBreakdown": {"summary": 42, "summaryPromptTokens": 42, "summaryCompletionTokens": 42, "structuredData": 42, "structuredDataPromptTokens": 42, "structuredDataCompletionTokens": 42, "successEvaluation": 42, "successEvaluationPromptTokens": 42, "successEvaluationCompletionTokens": 42}}, "artifactPlan": {"recordingEnabled": true, "recordingFormat": "wav;l16", "videoRecordingEnabled": false, "pcapEnabled": true, "pcapS3PathPrefix": "/pcaps", "transcriptPlan": {"enabled": true, "assistantName": "foo", "userName": "foo"}, "recordingPath": "foo"}, "analysis": {"summary": "foo", "structuredData": {}, "structuredDataMulti": [{}], "successEvaluation": "foo"}, "monitor": {"listenUrl": "foo", "controlUrl": "foo"}, "artifact": {"messages": [{"role": "foo", "message": "foo", "time": 42, "endTime": 42, "secondsFromStart": 42, "duration": 42}], "messagesOpenAIFormatted": [{"content": "foo", "role": "assistant"}], "recordingUrl": "foo", "stereoRecordingUrl": "foo", "videoRecordingUrl": "foo", "videoRecordingStartDelaySeconds": 42, "recording": {"stereoUrl": "foo", "videoUrl": "foo", "videoRecordingStartDelaySeconds": 42, "mono": {"combinedUrl": "foo", "assistantUrl": "foo", "customerUrl": "foo"}}, "transcript": "foo", "pcapUrl": "foo", "nodes": [{"messages": [{"role": "foo", "message": "foo", "time": 42, "endTime": 42, "secondsFromStart": 42, "duration": 42}], "nodeName": "foo", "variableValues": {}}], "variableValues": {}}, "phoneCallProviderId": "foo", "campaignId": "foo", "assistantId": "foo", "assistant": {"transcriber": {"provider": "assembly-ai", "language": "en", "confidenceThreshold": 0.4, "enableUniversalStreamingApi": false, "formatTurns": false, "endOfTurnConfidenceThreshold": 0.7, "minEndOfTurnSilenceWhenConfident": 160, "wordFinalizationMaxWaitTime": 160, "maxTurnSilence": 400, "realtimeUrl": "foo", "wordBoost": ["foo"], "endUtteranceSilenceThreshold": 42, "disablePartialTranscripts": true, "fallbackPlan": {"transcribers": [{"provider": "assembly-ai", "language": "en", "confidenceThreshold": 0.4, "enableUniversalStreamingApi": false, "formatTurns": false, "endOfTurnConfidenceThreshold": 0.7, "minEndOfTurnSilenceWhenConfident": 160, "wordFinalizationMaxWaitTime": 160, "maxTurnSilence": 400, "realtimeUrl": "foo", "wordBoost": ["foo"], "endUtteranceSilenceThreshold": 42, "disablePartialTranscripts": true}]}}, "model": {"messages": [{"content": "foo", "role": "assistant"}], "tools": [{"messages": [{"contents": [{"type": "text", "text": "foo", "language": "aa"}], "type": "request-start", "blocking": false, "content": "foo", "conditions": [{"operator": "eq", "param": "foo", "value": "foo"}]}], "type": "apiRequest", "method": "POST", "timeoutSeconds": 20, "name": "foo", "description": "foo", "url": "foo", "body": {"type": "string", "items": {}, "properties": {}, "description": "foo", "pattern": "foo", "format": "date-time", "required": ["foo"], "enum": ["foo"], "title": "foo"}, "headers": {"type": "string", "items": {}, "properties": {}, "description": "foo", "pattern": "foo", "format": "date-time", "required": ["foo"], "enum": ["foo"], "title": "foo"}, "backoffPlan": {"type": "fixed", "maxRetries": 0, "baseDelaySeconds": 1}, "variableExtractionPlan": {"schema": {"type": "string", "items": {}, "properties": {}, "description": "foo", "pattern": "foo", "format": "date-time", "required": ["foo"], "enum": ["foo"], "title": "foo"}, "aliases": [{"key": "foo", "value": "foo"}]}}], "toolIds": ["foo"], "knowledgeBase": {"provider": "custom-knowledge-base", "server": {"timeoutSeconds": 20, "url": "foo", "headers": {}, "backoffPlan": {"type": "fixed", "maxRetries": 0, "baseDelaySeconds": 1}}}, "knowledgeBaseId": "foo", "model": "claude-3-opus-20240229", "provider": "anthropic", "thinking": {"type": "enabled", "budgetTokens": 42}, "temperature": 42, "maxTokens": 42, "emotionRecognitionEnabled": true, "numFastTurns": 42}, "voice": {"cachingEnabled": true, "provider": "azure", "voiceId": "andrew", "chunkPlan": {"enabled": true, "minCharacters": 30, "punctuationBoundaries": "。", "formatPlan": {"enabled": true, "numberToDigitsCutoff": 2025, "replacements": [{"type": "exact", "replaceAllEnabled": false, "key": "foo", "value": "foo"}], "formattersEnabled": "markdown"}}, "speed": 42, "fallbackPlan": {"voices": [{"cachingEnabled": true, "provider": "azure", "voiceId": "andrew", "speed": 42, "chunkPlan": {"enabled": true, "minCharacters": 30, "punctuationBoundaries": "。", "formatPlan": {}}, "oneOf": null}]}}, "firstMessage": "Hello! How can I help you today?", "firstMessageInterruptionsEnabled": false, "firstMessageMode": "assistant-speaks-first", "voicemailDetection": {"beepMaxAwaitSeconds": 30, "provider": "google", "backoffPlan": {"startAtSeconds": 5, "frequencySeconds": 5, "maxRetries": 6}}, "clientMessages": "conversation-update", "serverMessages": "conversation-update", "maxDurationSeconds": 600, "backgroundSound": "off", "backgroundDenoisingEnabled": false, "modelOutputInMessagesEnabled": false, "transportConfigurations": [{"provider": "twi<PERSON>", "timeout": 60, "record": false, "recordingChannels": "mono"}], "observabilityPlan": {"provider": "langfuse", "tags": ["foo"], "metadata": {}}, "credentials": [{"provider": "anthropic", "apiKey": "foo", "name": "foo"}], "hooks": [{"on": "call.ending", "do": [{"type": "tool", "tool": {"messages": [{"contents": [{}], "type": "request-start", "blocking": false, "content": "foo", "conditions": [{"operator": {}, "param": {}, "value": {}}]}], "type": "apiRequest", "method": "POST", "timeoutSeconds": 20, "name": "foo", "description": "foo", "url": "foo", "body": {"type": "string", "items": {}, "properties": {}, "description": "foo", "pattern": "foo", "format": "date-time", "required": ["foo"], "enum": ["foo"], "title": "foo"}, "headers": {"type": "string", "items": {}, "properties": {}, "description": "foo", "pattern": "foo", "format": "date-time", "required": ["foo"], "enum": ["foo"], "title": "foo"}, "backoffPlan": {"type": "fixed", "maxRetries": 0, "baseDelaySeconds": 1}, "variableExtractionPlan": {"schema": {}, "aliases": [{"key": {}, "value": {}}]}}, "toolId": "foo"}], "filters": [{"type": "oneOf", "key": "foo", "oneOf": ["foo"]}]}], "name": "foo", "voicemailMessage": "foo", "endCallMessage": "foo", "endCallPhrases": ["foo"], "compliancePlan": {"hipaaEnabled": {"hipaaEnabled": false}, "pciEnabled": {"pciEnabled": false}}, "metadata": {}, "backgroundSpeechDenoisingPlan": {"smartDenoisingPlan": {"enabled": false}, "fourierDenoisingPlan": {"enabled": false, "mediaDetectionEnabled": true, "staticThreshold": -35, "baselineOffsetDb": -15, "windowSizeMs": 3000, "baselinePercentile": 85}}, "analysisPlan": {"minMessagesThreshold": 42, "summaryPlan": {"messages": [{}], "enabled": true, "timeoutSeconds": 42}, "structuredDataPlan": {"messages": [{}], "enabled": true, "schema": {"type": "string", "items": {}, "properties": {}, "description": "foo", "pattern": "foo", "format": "date-time", "required": ["foo"], "enum": ["foo"], "title": "foo"}, "timeoutSeconds": 42}, "structuredDataMultiPlan": [{"key": "foo", "plan": {"messages": [{}], "enabled": true, "schema": {"type": "string", "items": {}, "properties": {}, "description": "foo", "pattern": "foo", "format": "date-time", "required": ["foo"], "enum": ["foo"], "title": "foo"}, "timeoutSeconds": 42}}], "successEvaluationPlan": {"rubric": "NumericScale", "messages": [{}], "enabled": true, "timeoutSeconds": 42}}, "artifactPlan": {"recordingEnabled": true, "recordingFormat": "wav;l16", "videoRecordingEnabled": false, "pcapEnabled": true, "pcapS3PathPrefix": "/pcaps", "transcriptPlan": {"enabled": true, "assistantName": "foo", "userName": "foo"}, "recordingPath": "foo"}, "messagePlan": {"idleMessages": ["foo"], "idleMessageMaxSpokenCount": 42, "idleMessageResetCountOnUserSpeechEnabled": true, "idleTimeoutSeconds": 42, "silenceTimeoutMessage": "foo"}, "startSpeakingPlan": {"waitSeconds": 0.4, "smartEndpointingEnabled": false, "smartEndpointingPlan": {"provider": "vapi"}, "customEndpointingRules": [{"type": "assistant", "regex": "foo", "regexOptions": [{"type": "ignore-case", "enabled": true}], "timeoutSeconds": 42}], "transcriptionEndpointingPlan": {"onPunctuationSeconds": 0.1, "onNoPunctuationSeconds": 1.5, "onNumberSeconds": 0.5}}, "stopSpeakingPlan": {"numWords": 0, "voiceSeconds": 0.2, "backoffSeconds": 1, "acknowledgementPhrases": ["i understand", "i see", "i got it", "i hear you", "im listening", "im with you", "right", "okay", "ok", "sure", "alright", "got it", "understood", "yeah", "yes", "uh-huh", "mm-hmm", "gotcha", "mhmm", "ah", "yeah okay", "yeah sure"], "interruptionPhrases": ["stop", "shut", "up", "enough", "quiet", "silence", "but", "dont", "not", "no", "hold", "wait", "cut", "pause", "nope", "nah", "nevermind", "never", "bad", "actually"]}, "monitorPlan": {"listenEnabled": false, "listenAuthenticationEnabled": false, "controlEnabled": false, "controlAuthenticationEnabled": false}, "credentialIds": ["foo"], "server": {"timeoutSeconds": 20, "url": "foo", "headers": {}, "backoffPlan": {"type": "fixed", "maxRetries": 0, "baseDelaySeconds": 1}}, "keypadInputPlan": {"enabled": true, "timeoutSeconds": 42, "delimiters": "#"}}, "assistantOverrides": {"transcriber": {"provider": "assembly-ai", "language": "en", "confidenceThreshold": 0.4, "enableUniversalStreamingApi": false, "formatTurns": false, "endOfTurnConfidenceThreshold": 0.7, "minEndOfTurnSilenceWhenConfident": 160, "wordFinalizationMaxWaitTime": 160, "maxTurnSilence": 400, "realtimeUrl": "foo", "wordBoost": ["foo"], "endUtteranceSilenceThreshold": 42, "disablePartialTranscripts": true, "fallbackPlan": {"transcribers": [{"provider": "assembly-ai", "language": "en", "confidenceThreshold": 0.4, "enableUniversalStreamingApi": false, "formatTurns": false, "endOfTurnConfidenceThreshold": 0.7, "minEndOfTurnSilenceWhenConfident": 160, "wordFinalizationMaxWaitTime": 160, "maxTurnSilence": 400, "realtimeUrl": "foo", "wordBoost": ["foo"], "endUtteranceSilenceThreshold": 42, "disablePartialTranscripts": true}]}}, "model": {"messages": [{"content": "foo", "role": "assistant"}], "tools": [{"messages": [{"contents": [{"type": "text", "text": "foo", "language": "aa"}], "type": "request-start", "blocking": false, "content": "foo", "conditions": [{"operator": "eq", "param": "foo", "value": "foo"}]}], "type": "apiRequest", "method": "POST", "timeoutSeconds": 20, "name": "foo", "description": "foo", "url": "foo", "body": {"type": "string", "items": {}, "properties": {}, "description": "foo", "pattern": "foo", "format": "date-time", "required": ["foo"], "enum": ["foo"], "title": "foo"}, "headers": {"type": "string", "items": {}, "properties": {}, "description": "foo", "pattern": "foo", "format": "date-time", "required": ["foo"], "enum": ["foo"], "title": "foo"}, "backoffPlan": {"type": "fixed", "maxRetries": 0, "baseDelaySeconds": 1}, "variableExtractionPlan": {"schema": {"type": "string", "items": {}, "properties": {}, "description": "foo", "pattern": "foo", "format": "date-time", "required": ["foo"], "enum": ["foo"], "title": "foo"}, "aliases": [{"key": "foo", "value": "foo"}]}}], "toolIds": ["foo"], "knowledgeBase": {"provider": "custom-knowledge-base", "server": {"timeoutSeconds": 20, "url": "foo", "headers": {}, "backoffPlan": {"type": "fixed", "maxRetries": 0, "baseDelaySeconds": 1}}}, "knowledgeBaseId": "foo", "model": "claude-3-opus-20240229", "provider": "anthropic", "thinking": {"type": "enabled", "budgetTokens": 42}, "temperature": 42, "maxTokens": 42, "emotionRecognitionEnabled": true, "numFastTurns": 42}, "voice": {"cachingEnabled": true, "provider": "azure", "voiceId": "andrew", "chunkPlan": {"enabled": true, "minCharacters": 30, "punctuationBoundaries": "。", "formatPlan": {"enabled": true, "numberToDigitsCutoff": 2025, "replacements": [{"type": "exact", "replaceAllEnabled": false, "key": "foo", "value": "foo"}], "formattersEnabled": "markdown"}}, "speed": 42, "fallbackPlan": {"voices": [{"cachingEnabled": true, "provider": "azure", "voiceId": "andrew", "speed": 42, "chunkPlan": {"enabled": true, "minCharacters": 30, "punctuationBoundaries": "。", "formatPlan": {}}, "oneOf": null}]}}, "firstMessage": "Hello! How can I help you today?", "firstMessageInterruptionsEnabled": false, "firstMessageMode": "assistant-speaks-first", "voicemailDetection": {"beepMaxAwaitSeconds": 30, "provider": "google", "backoffPlan": {"startAtSeconds": 5, "frequencySeconds": 5, "maxRetries": 6}}, "clientMessages": "conversation-update", "serverMessages": "conversation-update", "maxDurationSeconds": 600, "backgroundSound": "off", "backgroundDenoisingEnabled": false, "modelOutputInMessagesEnabled": false, "transportConfigurations": [{"provider": "twi<PERSON>", "timeout": 60, "record": false, "recordingChannels": "mono"}], "observabilityPlan": {"provider": "langfuse", "tags": ["foo"], "metadata": {}}, "credentials": [{"provider": "anthropic", "apiKey": "foo", "name": "foo"}], "hooks": [{"on": "call.ending", "do": [{"type": "tool", "tool": {"messages": [{"contents": [{}], "type": "request-start", "blocking": false, "content": "foo", "conditions": [{"operator": {}, "param": {}, "value": {}}]}], "type": "apiRequest", "method": "POST", "timeoutSeconds": 20, "name": "foo", "description": "foo", "url": "foo", "body": {"type": "string", "items": {}, "properties": {}, "description": "foo", "pattern": "foo", "format": "date-time", "required": ["foo"], "enum": ["foo"], "title": "foo"}, "headers": {"type": "string", "items": {}, "properties": {}, "description": "foo", "pattern": "foo", "format": "date-time", "required": ["foo"], "enum": ["foo"], "title": "foo"}, "backoffPlan": {"type": "fixed", "maxRetries": 0, "baseDelaySeconds": 1}, "variableExtractionPlan": {"schema": {}, "aliases": [{"key": {}, "value": {}}]}}, "toolId": "foo"}], "filters": [{"type": "oneOf", "key": "foo", "oneOf": ["foo"]}]}], "variableValues": {}, "name": "foo", "voicemailMessage": "foo", "endCallMessage": "foo", "endCallPhrases": ["foo"], "compliancePlan": {"hipaaEnabled": {"hipaaEnabled": false}, "pciEnabled": {"pciEnabled": false}}, "metadata": {}, "backgroundSpeechDenoisingPlan": {"smartDenoisingPlan": {"enabled": false}, "fourierDenoisingPlan": {"enabled": false, "mediaDetectionEnabled": true, "staticThreshold": -35, "baselineOffsetDb": -15, "windowSizeMs": 3000, "baselinePercentile": 85}}, "analysisPlan": {"minMessagesThreshold": 42, "summaryPlan": {"messages": [{}], "enabled": true, "timeoutSeconds": 42}, "structuredDataPlan": {"messages": [{}], "enabled": true, "schema": {"type": "string", "items": {}, "properties": {}, "description": "foo", "pattern": "foo", "format": "date-time", "required": ["foo"], "enum": ["foo"], "title": "foo"}, "timeoutSeconds": 42}, "structuredDataMultiPlan": [{"key": "foo", "plan": {"messages": [{}], "enabled": true, "schema": {"type": "string", "items": {}, "properties": {}, "description": "foo", "pattern": "foo", "format": "date-time", "required": ["foo"], "enum": ["foo"], "title": "foo"}, "timeoutSeconds": 42}}], "successEvaluationPlan": {"rubric": "NumericScale", "messages": [{}], "enabled": true, "timeoutSeconds": 42}}, "artifactPlan": {"recordingEnabled": true, "recordingFormat": "wav;l16", "videoRecordingEnabled": false, "pcapEnabled": true, "pcapS3PathPrefix": "/pcaps", "transcriptPlan": {"enabled": true, "assistantName": "foo", "userName": "foo"}, "recordingPath": "foo"}, "messagePlan": {"idleMessages": ["foo"], "idleMessageMaxSpokenCount": 42, "idleMessageResetCountOnUserSpeechEnabled": true, "idleTimeoutSeconds": 42, "silenceTimeoutMessage": "foo"}, "startSpeakingPlan": {"waitSeconds": 0.4, "smartEndpointingEnabled": false, "smartEndpointingPlan": {"provider": "vapi"}, "customEndpointingRules": [{"type": "assistant", "regex": "foo", "regexOptions": [{"type": "ignore-case", "enabled": true}], "timeoutSeconds": 42}], "transcriptionEndpointingPlan": {"onPunctuationSeconds": 0.1, "onNoPunctuationSeconds": 1.5, "onNumberSeconds": 0.5}}, "stopSpeakingPlan": {"numWords": 0, "voiceSeconds": 0.2, "backoffSeconds": 1, "acknowledgementPhrases": ["i understand", "i see", "i got it", "i hear you", "im listening", "im with you", "right", "okay", "ok", "sure", "alright", "got it", "understood", "yeah", "yes", "uh-huh", "mm-hmm", "gotcha", "mhmm", "ah", "yeah okay", "yeah sure"], "interruptionPhrases": ["stop", "shut", "up", "enough", "quiet", "silence", "but", "dont", "not", "no", "hold", "wait", "cut", "pause", "nope", "nah", "nevermind", "never", "bad", "actually"]}, "monitorPlan": {"listenEnabled": false, "listenAuthenticationEnabled": false, "controlEnabled": false, "controlAuthenticationEnabled": false}, "credentialIds": ["foo"], "server": {"timeoutSeconds": 20, "url": "foo", "headers": {}, "backoffPlan": {"type": "fixed", "maxRetries": 0, "baseDelaySeconds": 1}}, "keypadInputPlan": {"enabled": true, "timeoutSeconds": 42, "delimiters": "#"}}, "squadId": "foo", "squad": {"name": "foo", "members": [{"assistantId": "foo", "assistant": {"transcriber": {"provider": "assembly-ai", "language": "en", "confidenceThreshold": 0.4, "enableUniversalStreamingApi": false, "formatTurns": false, "endOfTurnConfidenceThreshold": 0.7, "minEndOfTurnSilenceWhenConfident": 160, "wordFinalizationMaxWaitTime": 160, "maxTurnSilence": 400, "realtimeUrl": "foo", "wordBoost": ["foo"], "endUtteranceSilenceThreshold": 42, "disablePartialTranscripts": true, "fallbackPlan": {"transcribers": [{"provider": {}, "language": {}, "confidenceThreshold": {}, "enableUniversalStreamingApi": {}, "formatTurns": {}, "endOfTurnConfidenceThreshold": {}, "minEndOfTurnSilenceWhenConfident": {}, "wordFinalizationMaxWaitTime": {}, "maxTurnSilence": {}, "realtimeUrl": {}, "wordBoost": {}, "endUtteranceSilenceThreshold": {}, "disablePartialTranscripts": {}}]}}, "model": {"messages": [{"content": "foo", "role": "assistant"}], "tools": [{"messages": [{"contents": {}, "type": {}, "blocking": {}, "content": {}, "conditions": {}}], "type": "apiRequest", "method": "POST", "timeoutSeconds": 20, "name": "foo", "description": "foo", "url": "foo", "body": {"type": {}, "items": {}, "properties": {}, "description": {}, "pattern": {}, "format": {}, "required": {}, "enum": {}, "title": {}}, "headers": {"type": {}, "items": {}, "properties": {}, "description": {}, "pattern": {}, "format": {}, "required": {}, "enum": {}, "title": {}}, "backoffPlan": {"type": {}, "maxRetries": {}, "baseDelaySeconds": {}}, "variableExtractionPlan": {"schema": {}, "aliases": {}}}], "toolIds": ["foo"], "knowledgeBase": {"provider": "custom-knowledge-base", "server": {"timeoutSeconds": {}, "url": {}, "headers": {}, "backoffPlan": {}}}, "knowledgeBaseId": "foo", "model": "claude-3-opus-20240229", "provider": "anthropic", "thinking": {"type": "enabled", "budgetTokens": 42}, "temperature": 42, "maxTokens": 42, "emotionRecognitionEnabled": true, "numFastTurns": 42}, "voice": {"cachingEnabled": true, "provider": "azure", "voiceId": "andrew", "chunkPlan": {"enabled": true, "minCharacters": 30, "punctuationBoundaries": "。", "formatPlan": {"enabled": {}, "numberToDigitsCutoff": {}, "replacements": {}, "formattersEnabled": {}}}, "speed": 42, "fallbackPlan": {"voices": [{"cachingEnabled": {}, "provider": {}, "voiceId": {}, "speed": {}, "chunkPlan": {}, "oneOf": {}}]}}, "firstMessage": "Hello! How can I help you today?", "firstMessageInterruptionsEnabled": false, "firstMessageMode": "assistant-speaks-first", "voicemailDetection": {"beepMaxAwaitSeconds": 30, "provider": "google", "backoffPlan": {"startAtSeconds": 5, "frequencySeconds": 5, "maxRetries": 6}}, "clientMessages": "conversation-update", "serverMessages": "conversation-update", "maxDurationSeconds": 600, "backgroundSound": "off", "backgroundDenoisingEnabled": false, "modelOutputInMessagesEnabled": false, "transportConfigurations": [{"provider": "twi<PERSON>", "timeout": 60, "record": false, "recordingChannels": "mono"}], "observabilityPlan": {"provider": "langfuse", "tags": ["foo"], "metadata": {}}, "credentials": [{"provider": "anthropic", "apiKey": "foo", "name": "foo"}], "hooks": [{"on": "call.ending", "do": [{"type": "tool", "tool": {"messages": {}, "type": {}, "method": {}, "timeoutSeconds": {}, "name": {}, "description": {}, "url": {}, "body": {}, "headers": {}, "backoffPlan": {}, "variableExtractionPlan": {}}, "toolId": "foo"}], "filters": [{"type": "oneOf", "key": "foo", "oneOf": ["foo"]}]}], "name": "foo", "voicemailMessage": "foo", "endCallMessage": "foo", "endCallPhrases": ["foo"], "compliancePlan": {"hipaaEnabled": {"hipaaEnabled": false}, "pciEnabled": {"pciEnabled": false}}, "metadata": {}, "backgroundSpeechDenoisingPlan": {"smartDenoisingPlan": {"enabled": false}, "fourierDenoisingPlan": {"enabled": false, "mediaDetectionEnabled": true, "staticThreshold": -35, "baselineOffsetDb": -15, "windowSizeMs": 3000, "baselinePercentile": 85}}, "analysisPlan": {"minMessagesThreshold": 42, "summaryPlan": {"messages": [{}], "enabled": true, "timeoutSeconds": 42}, "structuredDataPlan": {"messages": [{}], "enabled": true, "schema": {"type": {}, "items": {}, "properties": {}, "description": {}, "pattern": {}, "format": {}, "required": {}, "enum": {}, "title": {}}, "timeoutSeconds": 42}, "structuredDataMultiPlan": [{"key": "foo", "plan": {"messages": [{}], "enabled": true, "schema": {}, "timeoutSeconds": 42}}], "successEvaluationPlan": {"rubric": "NumericScale", "messages": [{}], "enabled": true, "timeoutSeconds": 42}}, "artifactPlan": {"recordingEnabled": true, "recordingFormat": "wav;l16", "videoRecordingEnabled": false, "pcapEnabled": true, "pcapS3PathPrefix": "/pcaps", "transcriptPlan": {"enabled": true, "assistantName": "foo", "userName": "foo"}, "recordingPath": "foo"}, "messagePlan": {"idleMessages": ["foo"], "idleMessageMaxSpokenCount": 42, "idleMessageResetCountOnUserSpeechEnabled": true, "idleTimeoutSeconds": 42, "silenceTimeoutMessage": "foo"}, "startSpeakingPlan": {"waitSeconds": 0.4, "smartEndpointingEnabled": false, "smartEndpointingPlan": {"provider": "vapi"}, "customEndpointingRules": [{"type": "assistant", "regex": "foo", "regexOptions": [{"type": "ignore-case", "enabled": true}], "timeoutSeconds": 42}], "transcriptionEndpointingPlan": {"onPunctuationSeconds": 0.1, "onNoPunctuationSeconds": 1.5, "onNumberSeconds": 0.5}}, "stopSpeakingPlan": {"numWords": 0, "voiceSeconds": 0.2, "backoffSeconds": 1, "acknowledgementPhrases": ["i understand", "i see", "i got it", "i hear you", "im listening", "im with you", "right", "okay", "ok", "sure", "alright", "got it", "understood", "yeah", "yes", "uh-huh", "mm-hmm", "gotcha", "mhmm", "ah", "yeah okay", "yeah sure"], "interruptionPhrases": ["stop", "shut", "up", "enough", "quiet", "silence", "but", "dont", "not", "no", "hold", "wait", "cut", "pause", "nope", "nah", "nevermind", "never", "bad", "actually"]}, "monitorPlan": {"listenEnabled": false, "listenAuthenticationEnabled": false, "controlEnabled": false, "controlAuthenticationEnabled": false}, "credentialIds": ["foo"], "server": {"timeoutSeconds": 20, "url": "foo", "headers": {}, "backoffPlan": {"type": "fixed", "maxRetries": 0, "baseDelaySeconds": 1}}, "keypadInputPlan": {"enabled": true, "timeoutSeconds": 42, "delimiters": "#"}}, "assistantOverrides": {"transcriber": {"provider": "assembly-ai", "language": "en", "confidenceThreshold": 0.4, "enableUniversalStreamingApi": false, "formatTurns": false, "endOfTurnConfidenceThreshold": 0.7, "minEndOfTurnSilenceWhenConfident": 160, "wordFinalizationMaxWaitTime": 160, "maxTurnSilence": 400, "realtimeUrl": "foo", "wordBoost": ["foo"], "endUtteranceSilenceThreshold": 42, "disablePartialTranscripts": true, "fallbackPlan": {"transcribers": [{"provider": {}, "language": {}, "confidenceThreshold": {}, "enableUniversalStreamingApi": {}, "formatTurns": {}, "endOfTurnConfidenceThreshold": {}, "minEndOfTurnSilenceWhenConfident": {}, "wordFinalizationMaxWaitTime": {}, "maxTurnSilence": {}, "realtimeUrl": {}, "wordBoost": {}, "endUtteranceSilenceThreshold": {}, "disablePartialTranscripts": {}}]}}, "model": {"messages": [{"content": "foo", "role": "assistant"}], "tools": [{"messages": [{"contents": {}, "type": {}, "blocking": {}, "content": {}, "conditions": {}}], "type": "apiRequest", "method": "POST", "timeoutSeconds": 20, "name": "foo", "description": "foo", "url": "foo", "body": {"type": {}, "items": {}, "properties": {}, "description": {}, "pattern": {}, "format": {}, "required": {}, "enum": {}, "title": {}}, "headers": {"type": {}, "items": {}, "properties": {}, "description": {}, "pattern": {}, "format": {}, "required": {}, "enum": {}, "title": {}}, "backoffPlan": {"type": {}, "maxRetries": {}, "baseDelaySeconds": {}}, "variableExtractionPlan": {"schema": {}, "aliases": {}}}], "toolIds": ["foo"], "knowledgeBase": {"provider": "custom-knowledge-base", "server": {"timeoutSeconds": {}, "url": {}, "headers": {}, "backoffPlan": {}}}, "knowledgeBaseId": "foo", "model": "claude-3-opus-20240229", "provider": "anthropic", "thinking": {"type": "enabled", "budgetTokens": 42}, "temperature": 42, "maxTokens": 42, "emotionRecognitionEnabled": true, "numFastTurns": 42}, "voice": {"cachingEnabled": true, "provider": "azure", "voiceId": "andrew", "chunkPlan": {"enabled": true, "minCharacters": 30, "punctuationBoundaries": "。", "formatPlan": {"enabled": {}, "numberToDigitsCutoff": {}, "replacements": {}, "formattersEnabled": {}}}, "speed": 42, "fallbackPlan": {"voices": [{"cachingEnabled": {}, "provider": {}, "voiceId": {}, "speed": {}, "chunkPlan": {}, "oneOf": {}}]}}, "firstMessage": "Hello! How can I help you today?", "firstMessageInterruptionsEnabled": false, "firstMessageMode": "assistant-speaks-first", "voicemailDetection": {"beepMaxAwaitSeconds": 30, "provider": "google", "backoffPlan": {"startAtSeconds": 5, "frequencySeconds": 5, "maxRetries": 6}}, "clientMessages": "conversation-update", "serverMessages": "conversation-update", "maxDurationSeconds": 600, "backgroundSound": "off", "backgroundDenoisingEnabled": false, "modelOutputInMessagesEnabled": false, "transportConfigurations": [{"provider": "twi<PERSON>", "timeout": 60, "record": false, "recordingChannels": "mono"}], "observabilityPlan": {"provider": "langfuse", "tags": ["foo"], "metadata": {}}, "credentials": [{"provider": "anthropic", "apiKey": "foo", "name": "foo"}], "hooks": [{"on": "call.ending", "do": [{"type": "tool", "tool": {"messages": {}, "type": {}, "method": {}, "timeoutSeconds": {}, "name": {}, "description": {}, "url": {}, "body": {}, "headers": {}, "backoffPlan": {}, "variableExtractionPlan": {}}, "toolId": "foo"}], "filters": [{"type": "oneOf", "key": "foo", "oneOf": ["foo"]}]}], "variableValues": {}, "name": "foo", "voicemailMessage": "foo", "endCallMessage": "foo", "endCallPhrases": ["foo"], "compliancePlan": {"hipaaEnabled": {"hipaaEnabled": false}, "pciEnabled": {"pciEnabled": false}}, "metadata": {}, "backgroundSpeechDenoisingPlan": {"smartDenoisingPlan": {"enabled": false}, "fourierDenoisingPlan": {"enabled": false, "mediaDetectionEnabled": true, "staticThreshold": -35, "baselineOffsetDb": -15, "windowSizeMs": 3000, "baselinePercentile": 85}}, "analysisPlan": {"minMessagesThreshold": 42, "summaryPlan": {"messages": [{}], "enabled": true, "timeoutSeconds": 42}, "structuredDataPlan": {"messages": [{}], "enabled": true, "schema": {"type": {}, "items": {}, "properties": {}, "description": {}, "pattern": {}, "format": {}, "required": {}, "enum": {}, "title": {}}, "timeoutSeconds": 42}, "structuredDataMultiPlan": [{"key": "foo", "plan": {"messages": [{}], "enabled": true, "schema": {}, "timeoutSeconds": 42}}], "successEvaluationPlan": {"rubric": "NumericScale", "messages": [{}], "enabled": true, "timeoutSeconds": 42}}, "artifactPlan": {"recordingEnabled": true, "recordingFormat": "wav;l16", "videoRecordingEnabled": false, "pcapEnabled": true, "pcapS3PathPrefix": "/pcaps", "transcriptPlan": {"enabled": true, "assistantName": "foo", "userName": "foo"}, "recordingPath": "foo"}, "messagePlan": {"idleMessages": ["foo"], "idleMessageMaxSpokenCount": 42, "idleMessageResetCountOnUserSpeechEnabled": true, "idleTimeoutSeconds": 42, "silenceTimeoutMessage": "foo"}, "startSpeakingPlan": {"waitSeconds": 0.4, "smartEndpointingEnabled": false, "smartEndpointingPlan": {"provider": "vapi"}, "customEndpointingRules": [{"type": "assistant", "regex": "foo", "regexOptions": [{"type": "ignore-case", "enabled": true}], "timeoutSeconds": 42}], "transcriptionEndpointingPlan": {"onPunctuationSeconds": 0.1, "onNoPunctuationSeconds": 1.5, "onNumberSeconds": 0.5}}, "stopSpeakingPlan": {"numWords": 0, "voiceSeconds": 0.2, "backoffSeconds": 1, "acknowledgementPhrases": ["i understand", "i see", "i got it", "i hear you", "im listening", "im with you", "right", "okay", "ok", "sure", "alright", "got it", "understood", "yeah", "yes", "uh-huh", "mm-hmm", "gotcha", "mhmm", "ah", "yeah okay", "yeah sure"], "interruptionPhrases": ["stop", "shut", "up", "enough", "quiet", "silence", "but", "dont", "not", "no", "hold", "wait", "cut", "pause", "nope", "nah", "nevermind", "never", "bad", "actually"]}, "monitorPlan": {"listenEnabled": false, "listenAuthenticationEnabled": false, "controlEnabled": false, "controlAuthenticationEnabled": false}, "credentialIds": ["foo"], "server": {"timeoutSeconds": 20, "url": "foo", "headers": {}, "backoffPlan": {"type": "fixed", "maxRetries": 0, "baseDelaySeconds": 1}}, "keypadInputPlan": {"enabled": true, "timeoutSeconds": 42, "delimiters": "#"}}, "assistantDestinations": [{"message": "foo", "type": "assistant", "transferMode": "rolling-history", "assistantName": "foo", "description": "foo"}]}], "membersOverrides": {"transcriber": {"provider": "assembly-ai", "language": "en", "confidenceThreshold": 0.4, "enableUniversalStreamingApi": false, "formatTurns": false, "endOfTurnConfidenceThreshold": 0.7, "minEndOfTurnSilenceWhenConfident": 160, "wordFinalizationMaxWaitTime": 160, "maxTurnSilence": 400, "realtimeUrl": "foo", "wordBoost": ["foo"], "endUtteranceSilenceThreshold": 42, "disablePartialTranscripts": true, "fallbackPlan": {"transcribers": [{"provider": "assembly-ai", "language": "en", "confidenceThreshold": 0.4, "enableUniversalStreamingApi": false, "formatTurns": false, "endOfTurnConfidenceThreshold": 0.7, "minEndOfTurnSilenceWhenConfident": 160, "wordFinalizationMaxWaitTime": 160, "maxTurnSilence": 400, "realtimeUrl": "foo", "wordBoost": ["foo"], "endUtteranceSilenceThreshold": 42, "disablePartialTranscripts": true}]}}, "model": {"messages": [{"content": "foo", "role": "assistant"}], "tools": [{"messages": [{"contents": [{}], "type": "request-start", "blocking": false, "content": "foo", "conditions": [{"operator": {}, "param": {}, "value": {}}]}], "type": "apiRequest", "method": "POST", "timeoutSeconds": 20, "name": "foo", "description": "foo", "url": "foo", "body": {"type": "string", "items": {}, "properties": {}, "description": "foo", "pattern": "foo", "format": "date-time", "required": ["foo"], "enum": ["foo"], "title": "foo"}, "headers": {"type": "string", "items": {}, "properties": {}, "description": "foo", "pattern": "foo", "format": "date-time", "required": ["foo"], "enum": ["foo"], "title": "foo"}, "backoffPlan": {"type": "fixed", "maxRetries": 0, "baseDelaySeconds": 1}, "variableExtractionPlan": {"schema": {}, "aliases": [{"key": {}, "value": {}}]}}], "toolIds": ["foo"], "knowledgeBase": {"provider": "custom-knowledge-base", "server": {"timeoutSeconds": 20, "url": "foo", "headers": {}, "backoffPlan": {}}}, "knowledgeBaseId": "foo", "model": "claude-3-opus-20240229", "provider": "anthropic", "thinking": {"type": "enabled", "budgetTokens": 42}, "temperature": 42, "maxTokens": 42, "emotionRecognitionEnabled": true, "numFastTurns": 42}, "voice": {"cachingEnabled": true, "provider": "azure", "voiceId": "andrew", "chunkPlan": {"enabled": true, "minCharacters": 30, "punctuationBoundaries": "。", "formatPlan": {"enabled": true, "numberToDigitsCutoff": 2025, "replacements": [{}], "formattersEnabled": "markdown"}}, "speed": 42, "fallbackPlan": {"voices": [{"cachingEnabled": true, "provider": "azure", "voiceId": {}, "speed": 42, "chunkPlan": {}, "oneOf": null}]}}, "firstMessage": "Hello! How can I help you today?", "firstMessageInterruptionsEnabled": false, "firstMessageMode": "assistant-speaks-first", "voicemailDetection": {"beepMaxAwaitSeconds": 30, "provider": "google", "backoffPlan": {"startAtSeconds": 5, "frequencySeconds": 5, "maxRetries": 6}}, "clientMessages": "conversation-update", "serverMessages": "conversation-update", "maxDurationSeconds": 600, "backgroundSound": "off", "backgroundDenoisingEnabled": false, "modelOutputInMessagesEnabled": false, "transportConfigurations": [{"provider": "twi<PERSON>", "timeout": 60, "record": false, "recordingChannels": "mono"}], "observabilityPlan": {"provider": "langfuse", "tags": ["foo"], "metadata": {}}, "credentials": [{"provider": "anthropic", "apiKey": "foo", "name": "foo"}], "hooks": [{"on": "call.ending", "do": [{"type": "tool", "tool": {"messages": [{}], "type": "apiRequest", "method": "POST", "timeoutSeconds": 20, "name": "foo", "description": "foo", "url": "foo", "body": {}, "headers": {}, "backoffPlan": {}, "variableExtractionPlan": {}}, "toolId": "foo"}], "filters": [{"type": "oneOf", "key": "foo", "oneOf": ["foo"]}]}], "variableValues": {}, "name": "foo", "voicemailMessage": "foo", "endCallMessage": "foo", "endCallPhrases": ["foo"], "compliancePlan": {"hipaaEnabled": {"hipaaEnabled": false}, "pciEnabled": {"pciEnabled": false}}, "metadata": {}, "backgroundSpeechDenoisingPlan": {"smartDenoisingPlan": {"enabled": false}, "fourierDenoisingPlan": {"enabled": false, "mediaDetectionEnabled": true, "staticThreshold": -35, "baselineOffsetDb": -15, "windowSizeMs": 3000, "baselinePercentile": 85}}, "analysisPlan": {"minMessagesThreshold": 42, "summaryPlan": {"messages": [{}], "enabled": true, "timeoutSeconds": 42}, "structuredDataPlan": {"messages": [{}], "enabled": true, "schema": {"type": "string", "items": {}, "properties": {}, "description": "foo", "pattern": "foo", "format": "date-time", "required": ["foo"], "enum": ["foo"], "title": "foo"}, "timeoutSeconds": 42}, "structuredDataMultiPlan": [{"key": "foo", "plan": {"messages": [{}], "enabled": true, "schema": {"type": {}, "items": {}, "properties": {}, "description": {}, "pattern": {}, "format": {}, "required": {}, "enum": {}, "title": {}}, "timeoutSeconds": 42}}], "successEvaluationPlan": {"rubric": "NumericScale", "messages": [{}], "enabled": true, "timeoutSeconds": 42}}, "artifactPlan": {"recordingEnabled": true, "recordingFormat": "wav;l16", "videoRecordingEnabled": false, "pcapEnabled": true, "pcapS3PathPrefix": "/pcaps", "transcriptPlan": {"enabled": true, "assistantName": "foo", "userName": "foo"}, "recordingPath": "foo"}, "messagePlan": {"idleMessages": ["foo"], "idleMessageMaxSpokenCount": 42, "idleMessageResetCountOnUserSpeechEnabled": true, "idleTimeoutSeconds": 42, "silenceTimeoutMessage": "foo"}, "startSpeakingPlan": {"waitSeconds": 0.4, "smartEndpointingEnabled": false, "smartEndpointingPlan": {"provider": "vapi"}, "customEndpointingRules": [{"type": "assistant", "regex": "foo", "regexOptions": [{"type": "ignore-case", "enabled": true}], "timeoutSeconds": 42}], "transcriptionEndpointingPlan": {"onPunctuationSeconds": 0.1, "onNoPunctuationSeconds": 1.5, "onNumberSeconds": 0.5}}, "stopSpeakingPlan": {"numWords": 0, "voiceSeconds": 0.2, "backoffSeconds": 1, "acknowledgementPhrases": ["i understand", "i see", "i got it", "i hear you", "im listening", "im with you", "right", "okay", "ok", "sure", "alright", "got it", "understood", "yeah", "yes", "uh-huh", "mm-hmm", "gotcha", "mhmm", "ah", "yeah okay", "yeah sure"], "interruptionPhrases": ["stop", "shut", "up", "enough", "quiet", "silence", "but", "dont", "not", "no", "hold", "wait", "cut", "pause", "nope", "nah", "nevermind", "never", "bad", "actually"]}, "monitorPlan": {"listenEnabled": false, "listenAuthenticationEnabled": false, "controlEnabled": false, "controlAuthenticationEnabled": false}, "credentialIds": ["foo"], "server": {"timeoutSeconds": 20, "url": "foo", "headers": {}, "backoffPlan": {"type": "fixed", "maxRetries": 0, "baseDelaySeconds": 1}}, "keypadInputPlan": {"enabled": true, "timeoutSeconds": 42, "delimiters": "#"}}}, "workflowId": "foo", "workflow": {"nodes": [{"type": "conversation", "model": {"provider": "openai", "model": "gpt-4.1-2025-04-14", "temperature": 42, "maxTokens": 42}, "transcriber": {"provider": "assembly-ai", "language": "en", "confidenceThreshold": 0.4, "enableUniversalStreamingApi": false, "formatTurns": false, "endOfTurnConfidenceThreshold": 0.7, "minEndOfTurnSilenceWhenConfident": 160, "wordFinalizationMaxWaitTime": 160, "maxTurnSilence": 400, "realtimeUrl": "foo", "wordBoost": ["foo"], "endUtteranceSilenceThreshold": 42, "disablePartialTranscripts": true, "fallbackPlan": {"transcribers": [{"provider": "assembly-ai", "language": "en", "confidenceThreshold": 0.4, "enableUniversalStreamingApi": false, "formatTurns": false, "endOfTurnConfidenceThreshold": 0.7, "minEndOfTurnSilenceWhenConfident": 160, "wordFinalizationMaxWaitTime": 160, "maxTurnSilence": 400, "realtimeUrl": "foo", "wordBoost": ["foo"], "endUtteranceSilenceThreshold": 42, "disablePartialTranscripts": true}]}}, "voice": {"cachingEnabled": true, "provider": "azure", "voiceId": "andrew", "chunkPlan": {"enabled": true, "minCharacters": 30, "punctuationBoundaries": "。", "formatPlan": {"enabled": true, "numberToDigitsCutoff": 2025, "replacements": [{}], "formattersEnabled": "markdown"}}, "speed": 42, "fallbackPlan": {"voices": [{"cachingEnabled": true, "provider": "azure", "voiceId": {}, "speed": 42, "chunkPlan": {}, "oneOf": null}]}}, "prompt": "foo", "globalNodePlan": {"enabled": false, "enterCondition": ""}, "variableExtractionPlan": {"schema": {"type": "string", "items": {}, "properties": {}, "description": "foo", "pattern": "foo", "format": "date-time", "required": ["foo"], "enum": ["foo"], "title": "foo"}, "aliases": [{"key": "foo", "value": "foo"}]}, "name": "foo", "isStart": true, "metadata": {}}], "transcriber": {"provider": "assembly-ai", "language": "en", "confidenceThreshold": 0.4, "enableUniversalStreamingApi": false, "formatTurns": false, "endOfTurnConfidenceThreshold": 0.7, "minEndOfTurnSilenceWhenConfident": 160, "wordFinalizationMaxWaitTime": 160, "maxTurnSilence": 400, "realtimeUrl": "foo", "wordBoost": ["foo"], "endUtteranceSilenceThreshold": 42, "disablePartialTranscripts": true, "fallbackPlan": {"transcribers": [{"provider": "assembly-ai", "language": "en", "confidenceThreshold": 0.4, "enableUniversalStreamingApi": false, "formatTurns": false, "endOfTurnConfidenceThreshold": 0.7, "minEndOfTurnSilenceWhenConfident": 160, "wordFinalizationMaxWaitTime": 160, "maxTurnSilence": 400, "realtimeUrl": "foo", "wordBoost": ["foo"], "endUtteranceSilenceThreshold": 42, "disablePartialTranscripts": true}]}}, "voice": {"cachingEnabled": true, "provider": "azure", "voiceId": "andrew", "chunkPlan": {"enabled": true, "minCharacters": 30, "punctuationBoundaries": "。", "formatPlan": {"enabled": true, "numberToDigitsCutoff": 2025, "replacements": [{"type": "exact", "replaceAllEnabled": false, "key": "foo", "value": "foo"}], "formattersEnabled": "markdown"}}, "speed": 42, "fallbackPlan": {"voices": [{"cachingEnabled": true, "provider": "azure", "voiceId": "andrew", "speed": 42, "chunkPlan": {"enabled": true, "minCharacters": 30, "punctuationBoundaries": "。", "formatPlan": {}}, "oneOf": null}]}}, "observabilityPlan": {"provider": "langfuse", "tags": ["foo"], "metadata": {}}, "backgroundSound": "off", "credentials": [{"provider": "anthropic", "apiKey": "foo", "name": "foo"}], "name": "foo", "edges": [{"condition": {"type": "ai", "prompt": "foo"}, "from": "foo", "to": "foo", "metadata": {}}], "globalPrompt": "foo", "server": {"timeoutSeconds": 20, "url": "foo", "headers": {}, "backoffPlan": {"type": "fixed", "maxRetries": 0, "baseDelaySeconds": 1}}, "compliancePlan": {"hipaaEnabled": {"hipaaEnabled": false}, "pciEnabled": {"pciEnabled": false}}, "analysisPlan": {"minMessagesThreshold": 42, "summaryPlan": {"messages": [{}], "enabled": true, "timeoutSeconds": 42}, "structuredDataPlan": {"messages": [{}], "enabled": true, "schema": {"type": "string", "items": {}, "properties": {}, "description": "foo", "pattern": "foo", "format": "date-time", "required": ["foo"], "enum": ["foo"], "title": "foo"}, "timeoutSeconds": 42}, "structuredDataMultiPlan": [{"key": "foo", "plan": {"messages": [{}], "enabled": true, "schema": {"type": "string", "items": {}, "properties": {}, "description": "foo", "pattern": "foo", "format": "date-time", "required": ["foo"], "enum": ["foo"], "title": "foo"}, "timeoutSeconds": 42}}], "successEvaluationPlan": {"rubric": "NumericScale", "messages": [{}], "enabled": true, "timeoutSeconds": 42}}, "artifactPlan": {"recordingEnabled": true, "recordingFormat": "wav;l16", "videoRecordingEnabled": false, "pcapEnabled": true, "pcapS3PathPrefix": "/pcaps", "transcriptPlan": {"enabled": true, "assistantName": "foo", "userName": "foo"}, "recordingPath": "foo"}, "startSpeakingPlan": {"waitSeconds": 0.4, "smartEndpointingEnabled": false, "smartEndpointingPlan": {"provider": "vapi"}, "customEndpointingRules": [{"type": "assistant", "regex": "foo", "regexOptions": [{"type": "ignore-case", "enabled": true}], "timeoutSeconds": 42}], "transcriptionEndpointingPlan": {"onPunctuationSeconds": 0.1, "onNoPunctuationSeconds": 1.5, "onNumberSeconds": 0.5}}, "stopSpeakingPlan": {"numWords": 0, "voiceSeconds": 0.2, "backoffSeconds": 1, "acknowledgementPhrases": ["i understand", "i see", "i got it", "i hear you", "im listening", "im with you", "right", "okay", "ok", "sure", "alright", "got it", "understood", "yeah", "yes", "uh-huh", "mm-hmm", "gotcha", "mhmm", "ah", "yeah okay", "yeah sure"], "interruptionPhrases": ["stop", "shut", "up", "enough", "quiet", "silence", "but", "dont", "not", "no", "hold", "wait", "cut", "pause", "nope", "nah", "nevermind", "never", "bad", "actually"]}, "monitorPlan": {"listenEnabled": false, "listenAuthenticationEnabled": false, "controlEnabled": false, "controlAuthenticationEnabled": false}, "backgroundSpeechDenoisingPlan": {"smartDenoisingPlan": {"enabled": false}, "fourierDenoisingPlan": {"enabled": false, "mediaDetectionEnabled": true, "staticThreshold": -35, "baselineOffsetDb": -15, "windowSizeMs": 3000, "baselinePercentile": 85}}, "credentialIds": ["foo"]}, "workflowOverrides": {"variableValues": {}}, "phoneNumberId": "foo", "phoneNumber": {"fallbackDestination": {"message": "foo", "type": "number", "numberE164CheckEnabled": true, "number": "foo", "extension": "foo", "callerId": "foo", "transferPlan": {"mode": "blind-transfer", "message": "foo", "timeout": 60, "sipVerb": "refer", "holdAudioUrl": "foo", "transferCompleteAudioUrl": "foo", "twiml": "foo", "summaryPlan": {"messages": [{}], "enabled": true, "timeoutSeconds": 42}, "sipHeadersInReferToEnabled": true, "fallbackPlan": {"message": "foo", "endCallEnabled": true}}, "description": "foo"}, "hooks": [{"on": "call.ringing", "do": [{"type": "transfer", "destination": {"message": "foo", "type": "number", "numberE164CheckEnabled": true, "number": "foo", "extension": "foo", "callerId": "foo", "transferPlan": {"mode": "blind-transfer", "message": {}, "timeout": 60, "sipVerb": "refer", "holdAudioUrl": "foo", "transferCompleteAudioUrl": "foo", "twiml": "foo", "summaryPlan": {}, "sipHeadersInReferToEnabled": true, "fallbackPlan": {}}, "description": "foo"}}]}], "smsEnabled": true, "twilioPhoneNumber": "foo", "twilioAccountSid": "foo", "twilioAuthToken": "foo", "twilioApiKey": "foo", "twilioApiSecret": "foo", "name": "foo", "assistantId": "foo", "workflowId": "foo", "squadId": "foo", "server": {"timeoutSeconds": 20, "url": "foo", "headers": {}, "backoffPlan": {"type": "fixed", "maxRetries": 0, "baseDelaySeconds": 1}}}, "customerId": "foo", "customer": {"numberE164CheckEnabled": true, "extension": "foo", "assistantOverrides": {"transcriber": {"provider": "assembly-ai", "language": "en", "confidenceThreshold": 0.4, "enableUniversalStreamingApi": false, "formatTurns": false, "endOfTurnConfidenceThreshold": 0.7, "minEndOfTurnSilenceWhenConfident": 160, "wordFinalizationMaxWaitTime": 160, "maxTurnSilence": 400, "realtimeUrl": "foo", "wordBoost": ["foo"], "endUtteranceSilenceThreshold": 42, "disablePartialTranscripts": true, "fallbackPlan": {"transcribers": [{"provider": "assembly-ai", "language": "en", "confidenceThreshold": 0.4, "enableUniversalStreamingApi": false, "formatTurns": false, "endOfTurnConfidenceThreshold": 0.7, "minEndOfTurnSilenceWhenConfident": 160, "wordFinalizationMaxWaitTime": 160, "maxTurnSilence": 400, "realtimeUrl": "foo", "wordBoost": ["foo"], "endUtteranceSilenceThreshold": 42, "disablePartialTranscripts": true}]}}, "model": {"messages": [{"content": "foo", "role": "assistant"}], "tools": [{"messages": [{"contents": [{}], "type": "request-start", "blocking": false, "content": "foo", "conditions": [{"operator": {}, "param": {}, "value": {}}]}], "type": "apiRequest", "method": "POST", "timeoutSeconds": 20, "name": "foo", "description": "foo", "url": "foo", "body": {"type": "string", "items": {}, "properties": {}, "description": "foo", "pattern": "foo", "format": "date-time", "required": ["foo"], "enum": ["foo"], "title": "foo"}, "headers": {"type": "string", "items": {}, "properties": {}, "description": "foo", "pattern": "foo", "format": "date-time", "required": ["foo"], "enum": ["foo"], "title": "foo"}, "backoffPlan": {"type": "fixed", "maxRetries": 0, "baseDelaySeconds": 1}, "variableExtractionPlan": {"schema": {}, "aliases": [{"key": {}, "value": {}}]}}], "toolIds": ["foo"], "knowledgeBase": {"provider": "custom-knowledge-base", "server": {"timeoutSeconds": 20, "url": "foo", "headers": {}, "backoffPlan": {}}}, "knowledgeBaseId": "foo", "model": "claude-3-opus-20240229", "provider": "anthropic", "thinking": {"type": "enabled", "budgetTokens": 42}, "temperature": 42, "maxTokens": 42, "emotionRecognitionEnabled": true, "numFastTurns": 42}, "voice": {"cachingEnabled": true, "provider": "azure", "voiceId": "andrew", "chunkPlan": {"enabled": true, "minCharacters": 30, "punctuationBoundaries": "。", "formatPlan": {"enabled": true, "numberToDigitsCutoff": 2025, "replacements": [{}], "formattersEnabled": "markdown"}}, "speed": 42, "fallbackPlan": {"voices": [{"cachingEnabled": true, "provider": "azure", "voiceId": {}, "speed": 42, "chunkPlan": {}, "oneOf": null}]}}, "firstMessage": "Hello! How can I help you today?", "firstMessageInterruptionsEnabled": false, "firstMessageMode": "assistant-speaks-first", "voicemailDetection": {"beepMaxAwaitSeconds": 30, "provider": "google", "backoffPlan": {"startAtSeconds": 5, "frequencySeconds": 5, "maxRetries": 6}}, "clientMessages": "conversation-update", "serverMessages": "conversation-update", "maxDurationSeconds": 600, "backgroundSound": "off", "backgroundDenoisingEnabled": false, "modelOutputInMessagesEnabled": false, "transportConfigurations": [{"provider": "twi<PERSON>", "timeout": 60, "record": false, "recordingChannels": "mono"}], "observabilityPlan": {"provider": "langfuse", "tags": ["foo"], "metadata": {}}, "credentials": [{"provider": "anthropic", "apiKey": "foo", "name": "foo"}], "hooks": [{"on": "call.ending", "do": [{"type": "tool", "tool": {"messages": [{}], "type": "apiRequest", "method": "POST", "timeoutSeconds": 20, "name": "foo", "description": "foo", "url": "foo", "body": {}, "headers": {}, "backoffPlan": {}, "variableExtractionPlan": {}}, "toolId": "foo"}], "filters": [{"type": "oneOf", "key": "foo", "oneOf": ["foo"]}]}], "variableValues": {}, "name": "foo", "voicemailMessage": "foo", "endCallMessage": "foo", "endCallPhrases": ["foo"], "compliancePlan": {"hipaaEnabled": {"hipaaEnabled": false}, "pciEnabled": {"pciEnabled": false}}, "metadata": {}, "backgroundSpeechDenoisingPlan": {"smartDenoisingPlan": {"enabled": false}, "fourierDenoisingPlan": {"enabled": false, "mediaDetectionEnabled": true, "staticThreshold": -35, "baselineOffsetDb": -15, "windowSizeMs": 3000, "baselinePercentile": 85}}, "analysisPlan": {"minMessagesThreshold": 42, "summaryPlan": {"messages": [{}], "enabled": true, "timeoutSeconds": 42}, "structuredDataPlan": {"messages": [{}], "enabled": true, "schema": {"type": "string", "items": {}, "properties": {}, "description": "foo", "pattern": "foo", "format": "date-time", "required": ["foo"], "enum": ["foo"], "title": "foo"}, "timeoutSeconds": 42}, "structuredDataMultiPlan": [{"key": "foo", "plan": {"messages": [{}], "enabled": true, "schema": {"type": {}, "items": {}, "properties": {}, "description": {}, "pattern": {}, "format": {}, "required": {}, "enum": {}, "title": {}}, "timeoutSeconds": 42}}], "successEvaluationPlan": {"rubric": "NumericScale", "messages": [{}], "enabled": true, "timeoutSeconds": 42}}, "artifactPlan": {"recordingEnabled": true, "recordingFormat": "wav;l16", "videoRecordingEnabled": false, "pcapEnabled": true, "pcapS3PathPrefix": "/pcaps", "transcriptPlan": {"enabled": true, "assistantName": "foo", "userName": "foo"}, "recordingPath": "foo"}, "messagePlan": {"idleMessages": ["foo"], "idleMessageMaxSpokenCount": 42, "idleMessageResetCountOnUserSpeechEnabled": true, "idleTimeoutSeconds": 42, "silenceTimeoutMessage": "foo"}, "startSpeakingPlan": {"waitSeconds": 0.4, "smartEndpointingEnabled": false, "smartEndpointingPlan": {"provider": "vapi"}, "customEndpointingRules": [{"type": "assistant", "regex": "foo", "regexOptions": [{"type": "ignore-case", "enabled": true}], "timeoutSeconds": 42}], "transcriptionEndpointingPlan": {"onPunctuationSeconds": 0.1, "onNoPunctuationSeconds": 1.5, "onNumberSeconds": 0.5}}, "stopSpeakingPlan": {"numWords": 0, "voiceSeconds": 0.2, "backoffSeconds": 1, "acknowledgementPhrases": ["i understand", "i see", "i got it", "i hear you", "im listening", "im with you", "right", "okay", "ok", "sure", "alright", "got it", "understood", "yeah", "yes", "uh-huh", "mm-hmm", "gotcha", "mhmm", "ah", "yeah okay", "yeah sure"], "interruptionPhrases": ["stop", "shut", "up", "enough", "quiet", "silence", "but", "dont", "not", "no", "hold", "wait", "cut", "pause", "nope", "nah", "nevermind", "never", "bad", "actually"]}, "monitorPlan": {"listenEnabled": false, "listenAuthenticationEnabled": false, "controlEnabled": false, "controlAuthenticationEnabled": false}, "credentialIds": ["foo"], "server": {"timeoutSeconds": 20, "url": "foo", "headers": {}, "backoffPlan": {"type": "fixed", "maxRetries": 0, "baseDelaySeconds": 1}}, "keypadInputPlan": {"enabled": true, "timeoutSeconds": 42, "delimiters": "#"}}, "number": "foo", "sipUri": "foo", "name": "foo", "email": "foo", "externalId": "foo"}, "name": "foo", "schedulePlan": {"earliestAt": "foo", "latestAt": "foo"}, "transport": {}}