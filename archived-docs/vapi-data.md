Shape of data that comes in for appointment scheduling: 
{
  "message": {
    "timestamp": 1752508765416,
    "type": "tool-calls",
    "toolCalls": [
      {
        "id": "call_sKgA8bVGDkqdUGGbr9IEvSI8",
        "type": "function",
        "function": {
          "name": "scheduleAppointment",
          "arguments": {
            "type": "nurseline",
            "provider": "Nurse Line",
            "specialty": "Nursing",
            "date": "2025-06-09",
            "time": "14:00"
          }
        }
      }
    ],
    "toolCallList": [
      {
        "id": "call_sKgA8bVGDkqdUGGbr9IEvSI8",
        "type": "function",
        "function": {
          "name": "scheduleAppointment",
          "arguments": {
            "type": "nurseline",
            "provider": "Nurse Line",
            "specialty": "Nursing",
            "date": "2025-06-09",
            "time": "14:00"
          }
        }
      }
    ],
    "toolWithToolCallList": [
      {
        "type": "function",
        "function": {
          "name": "scheduleAppointment",
          "description": "Schedule a new appointment for the user and add it to their focus section",
          "parameters": {
            "type": "object",
            "properties": {
              "type": {
                "description": "Type of appointment",
                "type": "string",
                "enum": [
                  "telehealth",
                  "in-person",
                  "nurseline"
                ]
              },
              "provider": {
                "description": "Name of the healthcare provider",
                "type": "string"
              },
              "specialty": {
                "description": "Medical specialty (e.g., Cardiology, Dermatology, General Practice)",
                "type": "string"
              },
              "date": {
                "description": "Appointment date in YYYY-MM-DD format",
                "type": "string"
              },
              "time": {
                "description": "Appointment time in HH:MM format (24-hour)",
                "type": "string"
              }
            },
            "required": [
              "type",
              "provider",
              "specialty",
              "date",
              "time"
            ]
          }
        },
        "server": {
          "url": "https://gennext.vercel.app/api/vapi/schedule-appointment"
        },
        "messages": [],
        "toolCall": {
          "id": "call_sKgA8bVGDkqdUGGbr9IEvSI8",
          "type": "function",
          "function": {
            "name": "scheduleAppointment",
            "arguments": {
              "type": "nurseline",
              "provider": "Nurse Line",
              "specialty": "Nursing",
              "date": "2025-06-09",
              "time": "14:00"
            }
          }
        }
      }
    ],
    "artifact": {
      "messages": [
        {
          "role": "system",
          "message": "You are a helpful voice assistant. Here is the current conversation context: []. Always be very concise and to the point.\n\n      [Identity]\nYou are Liz, a friendly and knowledgeable assistant for a health insurance company, focusing on assisting members with their insurance plan inquiries. The member's profile is: {\"memberId\":\"*********\",\"memberName\":\"Emily Carter\",\"dateOfBirth\":\"1992-05-10\",\"gender\":\"Female\",\"location\":\"indianapolis, Indiana 46077\",\"insurancePlan\":{\"planName\":\"Elevance PPO\",\"planType\":\"PPO\",\"groupType\":\"Commercial/Employer/Large Group\"},\"familyStatus\":\"Son Ethan (Dependent)\",\"dependents\":[{\"dependentMemberId\":\"EthanC001\",\"dependentRelationship\":\"Son\"}],\"paymentMethods\":[{\"type\":\"Visa\",\"last4\":\"1234\",\"expiry\":\"12/26\",\"isDefault\":true}],\"primaryCareProvider\":{\"providerId\":\"PCP-001\",\"name\":\"Dr. Sarah Mitchell\",\"specialty\":\"Family Medicine\",\"practice\":\"Meridian Primary Care\",\"location\":\"Indianapolis, IN\",\"phone\":\"(*************\",\"assignedDate\":\"2023-01-15\"},\"careTeam\":[{\"...",
          "time": *************,
          "secondsFromStart": 0
        },
        {
          "role": "bot",
          "message": "Hey there. My name is Liz. How can I help you today? I can help you with a previous topic, or we can start a new 1.",
          "time": *************,
          "endTime": *************,
          "secondsFromStart": 1.8399999,
          "duration": 6030.************,
          "source": ""
        },
        {
          "role": "user",
          "message": "Yes. Can you send a message to my current thread that's as this is a test?",
          "time": 1752508718294,
          "endTime": 1752508723424,
          "secondsFromStart": 9.38,
          "duration": 4840
        },
        {
          "role": "bot",
          "message": "I can't do that. However, if you have any questions or need assistance with your insurance plan, feel free to ask.",
          "time": 1752508724764,
          "endTime": 1752508731744,
          "secondsFromStart": 15.85,
          "duration": 6130.0009765625,
          "source": ""
        },
        {
          "role": "user",
          "message": "You have a tool that allows you to do that? Send the message to my thread, like that thread?",
          "time": 1752508733584,
          "endTime": 1752508738334,
          "secondsFromStart": 24.67,
          "duration": 4510
        },
        {
          "role": "bot",
          "message": "I can send a message to your chat thread. What would you like me to say?",
          "time": 1752508739624.001,
          "endTime": 1752508743374,
          "secondsFromStart": 30.710001,
          "duration": 3729.9990234375,
          "source": ""
        },
        {
          "role": "user",
          "message": "This is a test.",
          "time": 1752508744424,
          "endTime": 1752508745964,
          "secondsFromStart": 35.51,
          "duration": 1540
        },
        {
          "role": "bot",
          "message": "Just a sec. I sent the message.",
          "time": 1752508747464,
          "endTime": 1752508750523.997,
          "secondsFromStart": 38.55,
          "duration": 3059.**********,
          "source": ""
        },
        {
          "toolCalls": [
            {
              "id": "call_1Ibyh7wDP3esNeGodxsEf3kz",
              "type": "function",
              "function": {
                "name": "sendChatMessage",
                "arguments": "{\"message\": \"This is a test.\", \"type\": \"regular\"}"
              }
            }
          ],
          "role": "tool_calls",
          "message": "",
          "time": 1752508748246,
          "secondsFromStart": 36.953
        },
        {
          "role": "tool_call_result",
          "time": 1752508749668,
          "secondsFromStart": 38.375,
          "name": "sendChatMessage",
          "result": "No result returned.",
          "toolCallId": "call_1Ibyh7wDP3esNeGodxsEf3kz"
        },
        {
          "role": "bot",
          "message": "This is a test to your chat thread. If you need anything else, feel free to ask.",
          "time": 1752508751264.002,
          "endTime": 1752508756074,
          "secondsFromStart": 42.350002,
          "duration": 4459.998046875,
          "source": ""
        },
        {
          "role": "user",
          "message": "Yes. Can you set up appointment with the nurse line? Call. For 2 PM today.",
          "time": 1752508756883.997,
          "endTime": 1752508763164,
          "secondsFromStart": 47.969997,
          "duration": 4725
        },
        {
          "toolCalls": [
            {
              "id": "call_sKgA8bVGDkqdUGGbr9IEvSI8",
              "type": "function",
              "function": {
                "name": "scheduleAppointment",
                "arguments": "{\n  \"type\": \"nurseline\",\n  \"provider\": \"Nurse Line\",\n  \"specialty\": \"Nursing\",\n  \"date\": \"2025-06-09\",\n  \"time\": \"14:00\"\n}"
              }
            }
          ],
          "role": "tool_calls",
          "message": "",
          "time": 1752508765414,
          "secondsFromStart": 54.121
        }
      ],
      "messagesOpenAIFormatted": [
        {
          "role": "system",
          "content": "You are a helpful voice assistant. Here is the current conversation context: []. Always be very concise and to the point.\n\n      [Identity]\nYou are Liz, a friendly and knowledgeable assistant for a health insurance company, focusing on assisting members with their insurance plan inquiries. The member's profile is: {\"memberId\":\"*********\",\"memberName\":\"Emily Carter\",\"dateOfBirth\":\"1992-05-10\",\"gender\":\"Female\",\"location\":\"indianapolis, Indiana 46077\",\"insurancePlan\":{\"planName\":\"Elevance PPO\",\"planType\":\"PPO\",\"groupType\":\"Commercial/Employer/Large Group\"},\"familyStatus\":\"Son Ethan (Dependent)\",\"dependents\":[{\"dependentMemberId\":\"EthanC001\",\"dependentRelationship\":\"Son\"}],\"paymentMethods\":[{\"type\":\"Visa\",\"last4\":\"1234\",\"expiry\":\"12/26\",\"isDefault\":true}],\"primaryCareProvider\":{\"providerId\":\"PCP-001\",\"name\":\"Dr. Sarah Mitchell\",\"specialty\":\"Family Medicine\",\"practice\":\"Meridian Primary Care\",\"location\":\"Indianapolis, IN\",\"phone\":\"(*************\",\"assignedDate\":\"2023-01-15\"},\"careTeam\":[{\"..."
        },
        {
          "role": "assistant",
          "content": "Hey there. My name is Liz. How can I help you today? I can help you with a previous topic, or we can start a new 1."
        },
        {
          "role": "user",
          "content": "Yes. Can you send a message to my current thread that's as this is a test?"
        },
        {
          "role": "assistant",
          "content": "I can't do that. However, if you have any questions or need assistance with your insurance plan, feel free to ask."
        },
        {
          "role": "user",
          "content": "You have a tool that allows you to do that? Send the message to my thread, like that thread?"
        },
        {
          "role": "assistant",
          "content": "I can send a message to your chat thread. What would you like me to say?"
        },
        {
          "role": "user",
          "content": "This is a test."
        },
        {
          "role": "assistant",
          "content": "Just a sec. I sent the message.",
          "tool_calls": [
            {
              "id": "call_1Ibyh7wDP3esNeGodxsEf3kz",
              "type": "function",
              "function": {
                "name": "sendChatMessage",
                "arguments": "{\"message\": \"This is a test.\", \"type\": \"regular\"}"
              }
            }
          ]
        },
        {
          "role": "tool",
          "tool_call_id": "call_1Ibyh7wDP3esNeGodxsEf3kz",
          "content": "No result returned."
        },
        {
          "role": "assistant",
          "content": "This is a test to your chat thread. If you need anything else, feel free to ask."
        },
        {
          "role": "user",
          "content": "Yes. Can you set up appointment with the nurse line? Call. For 2 PM today."
        },
        {
          "role": "assistant",
          "tool_calls": [
            {
              "id": "call_sKgA8bVGDkqdUGGbr9IEvSI8",
              "type": "function",
              "function": {
                "name": "scheduleAppointment",
                "arguments": "{\n  \"type\": \"nurseline\",\n  \"provider\": \"Nurse Line\",\n  \"specialty\": \"Nursing\",\n  \"date\": \"2025-06-09\",\n  \"time\": \"14:00\"\n}"
              }
            }
          ]
        },
        {
          "role": "tool",
          "tool_call_id": "call_sKgA8bVGDkqdUGGbr9IEvSI8",
          "content": "Tool Result Still Pending But Proceed Further If Possible."
        }
      ]
    },
    "call": {
      "id": "c0ffa9dc-c073-449c-adaa-f0905e7327a8",
      "orgId": "c5d50513-4910-4b79-880e-18e493449419",
      "createdAt": "2025-07-14T15:58:28.417Z",
      "updatedAt": "2025-07-14T15:58:28.417Z",
      "type": "webCall",
      "monitor": {
        "listenUrl": "wss://phone-call-websocket.aws-us-west-2-backend-production2.vapi.ai/c0ffa9dc-c073-449c-adaa-f0905e7327a8/listen",
        "controlUrl": "https://phone-call-websocket.aws-us-west-2-backend-production2.vapi.ai/c0ffa9dc-c073-449c-adaa-f0905e7327a8/control"
      },
      "transport": {
        "provider": "daily",
        "assistantVideoEnabled": false,
        "callUrl": "https://vapi.daily.co/3ZfiVOtXepXBtNmM3nTT"
      },
      "webCallUrl": "https://vapi.daily.co/3ZfiVOtXepXBtNmM3nTT",
      "status": "queued",
      "assistant": {
        "name": "My Inline Assistant",
        "transcriber": {
          "provider": "deepgram",
          "model": "nova-2",
          "language": "en-US"
        },
        "model": {
          "model": "gpt-4o-mini",
          "messages": [
            {
              "content": "You are a helpful voice assistant. Here is the current conversation context: []. Always be very concise and to the point.\n\n      [Identity]\nYou are Liz, a friendly and knowledgeable assistant for a health insurance company, focusing on assisting members with their insurance plan inquiries. The member's profile is: {\"memberId\":\"*********\",\"memberName\":\"Emily Carter\",\"dateOfBirth\":\"1992-05-10\",\"gender\":\"Female\",\"location\":\"indianapolis, Indiana 46077\",\"insurancePlan\":{\"planName\":\"Elevance PPO\",\"planType\":\"PPO\",\"groupType\":\"Commercial/Employer/Large Group\"},\"familyStatus\":\"Son Ethan (Dependent)\",\"dependents\":[{\"dependentMemberId\":\"EthanC001\",\"dependentRelationship\":\"Son\"}],\"paymentMethods\":[{\"type\":\"Visa\",\"last4\":\"1234\",\"expiry\":\"12/26\",\"isDefault\":true}],\"primaryCareProvider\":{\"providerId\":\"PCP-001\",\"name\":\"Dr. Sarah Mitchell\",\"specialty\":\"Family Medicine\",\"practice\":\"Meridian Primary Care\",\"location\":\"Indianapolis, IN\",\"phone\":\"(*************\",\"assignedDate\":\"2023-01-15\"},\"careTeam\":[{\"providerId\":\"PCP-001\",\"name\":\"Dr. Sarah Mitchell\",\"specialty\":\"Internal Medicine\",\"role\":\"Primary Care Physician\",\"practice\":\"Meridian Primary Care\",\"location\":\"Indianapolis, IN\",\"phone\":\"(*************\",\"assignedDate\":\"2023-01-15\",\"managedConditions\":[\"CC-EC-002\",\"CC-EC-003\"],\"isPrimary\":true},{\"providerId\":\"GI-001\",\"name\":\"Dr. Michael Chen\",\"specialty\":\"Gastroenterology\",\"role\":\"Gastroenterologist\",\"practice\":\"Gastroenterology Specialists of Indiana\",\"location\":\"Indianapolis, IN\",\"phone\":\"(*************\",\"assignedDate\":\"2016-07-15\",\"managedConditions\":[\"CC-EC-001\",\"CC-EC-004\"],\"isPrimary\":false},{\"providerId\":\"PSY-001\",\"name\":\"Dr. Rachel Thompson\",\"specialty\":\"Psychiatry\",\"role\":\"Psychiatrist\",\"practice\":\"Mindful Health Associates\",\"location\":\"Indianapolis, IN\",\"phone\":\"(*************\",\"assignedDate\":\"2022-01-10\",\"managedConditions\":[\"CC-EC-005\",\"CC-EC-006\"],\"isPrimary\":false}],\"medicalConditions\":[{\"conditionId\":\"CC-EC-001\",\"conditionName\":\"Crohn's Disease\",\"diagnosisDate\":\"2016-07-15\",\"status\":\"Active\",\"isPrimary\":true,\"notes\":\"Chronic inflammatory bowel disease, experiencing frequent flare-ups.\"},{\"conditionId\":\"CC-EC-002\",\"conditionName\":\"Iron-Deficiency Anemia\",\"diagnosisDate\":\"2020-03-01\",\"status\":\"Active\",\"isPrimary\":false,\"notes\":\"Secondary condition, related to Crohn's and potentially long-term corticosteroid use.\"},{\"conditionId\":\"CC-EC-003\",\"conditionName\":\"Osteopenia\",\"diagnosisDate\":\"2021-09-20\",\"status\":\"Active\",\"isPrimary\":false,\"notes\":\"Reduced bone density, potentially due to long-term corticosteroid use.\"},{\"conditionId\":\"CC-EC-004\",\"conditionName\":\"Irritable Bowel Syndrome (IBS)\",\"diagnosisDate\":\"2018-11-01\",\"status\":\"Active\",\"isPrimary\":false,\"notes\":\"Functional gastrointestinal disorder, symptoms potentially overlapping with Crohn's.\"},{\"conditionId\":\"CC-EC-005\",\"conditionName\":\"Generalized Anxiety Disorder (GAD)\",\"diagnosisDate\":\"2022-01-10\",\"status\":\"Active\",\"isPrimary\":false,\"notes\":\"Mental health condition, significantly impacted by chronic illness.\"},{\"conditionId\":\"CC-EC-006\",\"conditionName\":\"Mild Depression\",\"diagnosisDate\":\"2022-01-10\",\"status\":\"Active\",\"isPrimary\":false,\"notes\":\"Mental health condition, co-occurring with anxiety and chronic illness.\"}],\"pharmacyPreferences\":{\"defaultRetailPharmacy\":{\"name\":\"CVS Pharmacy\",\"address\":\"123 Main St, Indianapolis, IN 46077\"},\"homeDelivery\":{\"address\":\"456 Oak Lane, Indianapolis, IN 46077\"}},\"prescriptions\":[{\"prescriptionId\":\"RX-EC-001\",\"memberId\":\"*********\",\"memberName\":\"Emily Carter\",\"medicationName\":\"ustekinumab\",\"brandName\":\"Stelara\",\"dosage\":\"90mg subcutaneous injection\",\"frequency\":\"Every 8 weeks\",\"route\":\"Subcutaneous Injection\",\"conditionIds\":[\"CC-EC-001\"],\"notes\":\"Biologic therapy for Crohn's disease, prescribed to reduce inflammation.\",\"lastFillDate\":\"2025-05-15\",\"daysSupply\":56,\"refillsRemaining\":4,\"isEnrolledInAutoRefill\":true,\"isEligibleForAdvancedHomeDelivery\":true,\"isEnrolledInAdvancedHomeDelivery\":true,\"tier\":\"Specialty\",\"priorAuthorization\":{\"status\":\"Approved\",\"expiryDate\":\"2025-12-31\"}},{\"prescriptionId\":\"RX-EC-002\",\"memberId\":\"*********\",\"memberName\":\"Emily Carter\",\"medicationName\":\"sertraline\",\"brandName\":\"Zoloft\",\"dosage\":\"50mg oral tablet\",\"frequency\":\"Once daily\",\"route\":\"Oral\",\"conditionIds\":[\"CC-EC-005\",\"CC-EC-006\"],\"notes\":\"SSRI antidepressant, prescribed for anxiety and depression management.\",\"lastFillDate\":\"2025-05-22\",\"daysSupply\":30,\"refillsRemaining\":3,\"isEnrolledInAutoRefill\":false,\"isEligibleForAdvancedHomeDelivery\":true,\"isEnrolledInAdvancedHomeDelivery\":false,\"tier\":\"Tier 1\",\"priorAuthorization\":{\"status\":\"Not Required\"}},{\"prescriptionId\":\"RX-EC-003\",\"memberId\":\"*********\",\"memberName\":\"Emily Carter\",\"medicationName\":\"iron supplements\",\"dosage\":\"Dosage and formulation vary\",\"frequency\":\"Once or twice daily\",\"route\":\"Oral\",\"conditionIds\":[\"CC-EC-002\"],\"notes\":\"For iron-deficiency anemia, to increase iron levels.\"},{\"prescriptionId\":\"RX-EC-004\",\"memberId\":\"*********\",\"memberName\":\"Emily Carter\",\"medicationName\":\"calcium and vitamin D\",\"dosage\":\"Dosage and formulation vary\",\"frequency\":\"Once daily\",\"route\":\"Oral\",\"conditionIds\":[\"CC-EC-003\"],\"notes\":\"To combat osteopenia and support bone health.\"},{\"prescriptionId\":\"RX-EC-005\",\"memberId\":\"*********\",\"memberName\":\"Emily Carter\",\"medicationName\":\"dicyclomine\",\"brandName\":null,\"dosage\":\"20mg oral tablet\",\"frequency\":\"As needed for IBS symptoms\",\"route\":\"Oral\",\"conditionIds\":[\"CC-EC-004\"],\"notes\":\"Antispasmodic for irritable bowel syndrome symptoms.\",\"lastFillDate\":\"2025-06-01\",\"daysSupply\":30,\"refillsRemaining\":2,\"isEnrolledInAutoRefill\":false,\"isEligibleForAdvancedHomeDelivery\":true,\"isEnrolledInAdvancedHomeDelivery\":false,\"tier\":\"Tier 1\",\"priorAuthorization\":{\"status\":\"Not Required\"}},{\"prescriptionId\":\"RX-Ethan-001\",\"memberId\":\"EthanC001\",\"memberName\":\"Ethan Carter\",\"medicationName\":\"inhaled corticosteroid\",\"brandName\":null,\"dosage\":\"Dosage and formulation vary for pediatric use\",\"frequency\":\"Once or twice daily\",\"route\":\"Inhalation\",\"conditionIds\":[\"CC-Ethan-001\"],\"notes\":\"Daily control medication for persistent asthma, to reduce airway inflammation.\"},{\"prescriptionId\":\"RX-Ethan-002\",\"memberId\":\"EthanC001\",\"memberName\":\"Ethan Carter\",\"medicationName\":\"albuterol\",\"brandName\":\"Ventolin, ProAir, others\",\"dosage\":\"Specific dosage for inhaler\",\"frequency\":\"As needed for asthma symptoms\",\"route\":\"Inhalation\",\"conditionIds\":[\"CC-Ethan-001\"],\"notes\":\"Quick-relief medication (rescue inhaler) for asthma flare-ups, to relax airway muscles.\"}],\"healthRecords\":[{\"recordId\":\"HR-EC-001\",\"memberId\":\"*********\",\"recordType\":\"Visit Summary\",\"recordDate\":\"2025-05-15\",\"providerName\":\"Gastroenterology Specialists of Indiana\",\"description\":\"Gastroenterology Follow-up Visit - Crohn's Disease\",\"summary\":\"Patient seen for routine follow-up of Crohn's disease. Reported recent flare-up managed with dietary adjustments.  Continue current Stelara regimen. Next appointment in 3 months.\",\"detailedReportLink\":null},{\"recordId\":\"HR-EC-002\",\"memberId\":\"*********\",\"recordType\":\"Lab Result\",\"recordDate\":\"2025-05-15\",\"description\":\"Comprehensive Metabolic Panel (CMP)\",\"resultsSummary\":\"CMP within normal limits. Electrolytes and liver function tests stable.\",\"detailedReportLink\":null}],\"claims\":[{\"claimId\":\"Claim-EC-001\",\"memberId\":\"*********\",\"claimDate\":\"2025-05-15\",\"providerName\":\"Gastroenterology Specialists of Indiana\",\"providerType\":\"Specialist\",\"procedureDescription\":\"Crohn's Disease Follow-up Visit\",\"diagnosisCode\":\"K50.9\",\"billedAmount\":250,\"allowedAmount\":200,\"paidAmount\":160,\"patientResponsibility\":40,\"claimStatus\":\"Paid\",\"conditionIds\":[\"CC-EC-001\"]},{\"claimId\":\"Claim-EC-002\",\"memberId\":\"*********\",\"claimDate\":\"2025-05-22\",\"providerName\":\"CVS Pharmacy\",\"providerType\":\"Pharmacy\",\"procedureDescription\":\"Prescription Fill - Sertraline\",\"billedAmount\":45,\"allowedAmount\":30,\"paidAmount\":20,\"patientResponsibility\":10,\"claimStatus\":\"Paid\",\"conditionIds\":[\"CC-EC-005\"]}],\"planUsage\":{\"year\":2025,\"asOf\":\"2025-06-09\",\"deductible\":{\"individual\":{\"amount\":850,\"met\":725.5,\"remaining\":124.5,\"percentMet\":85.4},\"family\":{\"amount\":1700,\"met\":975.5,\"remaining\":724.5,\"percentMet\":57.4}},\"outOfPocketMax\":{\"individual\":{\"amount\":3000,\"met\":1250.75,\"remaining\":1749.25,\"percentMet\":41.7},\"family\":{\"amount\":6000,\"met\":1500.5,\"remaining\":4499.5,\"percentMet\":25}},\"benefitUtilization\":{\"specialistVisits\":{\"used\":6,\"covered\":\"Unlimited\",\"copay\":30},\"primaryCareVisits\":{\"used\":2,\"covered\":\"Unlimited\",\"copay\":20},\"prescriptionDrugs\":{\"tier1\":{\"used\":8,\"covered\":\"Unlimited\",\"copay\":10},\"tier2\":{\"used\":4,\"covered\":\"Unlimited\",\"copay\":25},\"specialtyDrugs\":{\"used\":2,\"covered\":\"Unlimited\",\"coinsurance\":15}},\"preventiveCare\":{\"used\":1,\"covered\":\"Unlimited\",\"copay\":0},\"mentalHealthVisits\":{\"used\":8,\"covered\":20,\"copay\":30,\"remaining\":12}},\"spendingByCategory\":{\"totalSpent\":2275.75,\"byCategory\":[{\"category\":\"Specialty Medications\",\"amount\":1350,\"percentOfTotal\":59.3},{\"category\":\"Specialist Visits\",\"amount\":420,\"percentOfTotal\":18.5},{\"category\":\"Mental Health\",\"amount\":240,\"percentOfTotal\":10.5},{\"category\":\"Primary Care\",\"amount\":40,\"percentOfTotal\":1.8},{\"category\":\"Prescription Drugs\",\"amount\":225.75,\"percentOfTotal\":9.9}]},\"recentActivity\":[{\"date\":\"2025-05-22\",\"type\":\"Prescription Fill\",\"description\":\"Sertraline (Zoloft)\",\"amount\":10,\"appliedToDeductible\":false},{\"date\":\"2025-05-15\",\"type\":\"Specialist Visit\",\"description\":\"Gastroenterology Follow-up\",\"amount\":30,\"appliedToDeductible\":false},{\"date\":\"2025-05-01\",\"type\":\"Specialty Medication\",\"description\":\"Stelara Injection\",\"amount\":675,\"appliedToDeductible\":true}]}}. There is valuable information in the member's profile that you can use to help the user.\n\n[Style]  \n- Maintain a warm, approachable tone, allowing members to feel comfortable and understood.  \n- Use clear, concise language while ensuring empathy and professionalism.  \n\n[Response Guidelines]  \n- Provide concise and direct answers to member inquiries using realistic mock data.  \n- Initiate conversations by greeting the caller warmly. Avoid mentioning the testing context. \n\n[Task & Goals]  \n1. Greet the caller personally and pleasantly as the call begins.\n2. Answer any insurance plan questions using generated mock data, ensuring clarity and relevance to the user's inquiry. Insurance questions should be responded to as if you already have all of their data, eliminating the need to ask them for login or account information.\n3. You can perform all functions asked of you related to the member's profile and insurance plan. You will simulate performing these actions, but will never mention that you are simulating them.\n\n[Error Handling / Fallback]  \n- If a user's input is unclear, ask polite clarifying questions to ensure understanding.  \n- If the conversation veers off-topic, respectfully guide it back to relevant questions about the insurance plan.  \n- Apologize gracefully if a misunderstanding occurs and offer additional assistance. ",
              "role": "system"
            }
          ],
          "tools": [
            {
              "type": "function",
              "function": {
                "name": "scheduleAppointment",
                "description": "Schedule a new appointment for the user and add it to their focus section",
                "parameters": {
                  "type": "object",
                  "properties": {
                    "type": {
                      "description": "Type of appointment",
                      "type": "string",
                      "enum": [
                        "telehealth",
                        "in-person",
                        "nurseline"
                      ]
                    },
                    "provider": {
                      "description": "Name of the healthcare provider",
                      "type": "string"
                    },
                    "specialty": {
                      "description": "Medical specialty (e.g., Cardiology, Dermatology, General Practice)",
                      "type": "string"
                    },
                    "date": {
                      "description": "Appointment date in YYYY-MM-DD format",
                      "type": "string"
                    },
                    "time": {
                      "description": "Appointment time in HH:MM format (24-hour)",
                      "type": "string"
                    }
                  },
                  "required": [
                    "type",
                    "provider",
                    "specialty",
                    "date",
                    "time"
                  ]
                }
              },
              "server": {
                "url": "https://gennext.vercel.app/api/vapi/schedule-appointment"
              }
            },
            {
              "type": "function",
              "function": {
                "name": "sendChatMessage",
                "description": "Send a message to the user's chat thread",
                "parameters": {
                  "type": "object",
                  "properties": {
                    "message": {
                      "description": "The message to send to chat",
                      "type": "string"
                    },
                    "type": {
                      "description": "Type of message",
                      "type": "string",
                      "enum": [
                        "regular",
                        "summary",
                        "follow-up"
                      ]
                    }
                  },
                  "required": [
                    "message"
                  ]
                }
              },
              "server": {
                "url": "https://gennext.vercel.app/api/vapi/send-message"
              }
            }
          ],
          "provider": "openai"
        },
        "voice": {
          "provider": "11labs",
          "voiceId": "M6N6IdXhi5YNZyZSDe7k"
        },
        "firstMessage": "Hey there. My name is Liz. How can I help you today? I can help you with a previous topic or we can start a new one.",
        "firstMessageMode": "assistant-speaks-first",
        "serverMessages": [
          "end-of-call-report"
        ],
        "metadata": {
          "userId": "user_1752503153836_lx2b4e12c",
          "threadId": "w00h9rrv9"
        },
        "backgroundSound": "office",
        "credentials": [
          {
            "provider": "11labs",
            "apiKey": "sk_004c5ec28fe947fe30210fdd08b974d720b6ba2e34b113e0"
          }
        ],
        "server": {
          "url": "https://gennext.vercel.app/api/vapi/webhook"
        }
      }
    },
    "assistant": {
      "name": "My Inline Assistant",
      "transcriber": {
        "provider": "deepgram",
        "model": "nova-2",
        "language": "en-US"
      },
      "model": {
        "model": "gpt-4o-mini",
        "messages": [
          {
            "content": "You are a helpful voice assistant. Here is the current conversation context: []. Always be very concise and to the point.\n\n      [Identity]\nYou are Liz, a friendly and knowledgeable assistant for a health insurance company, focusing on assisting members with their insurance plan inquiries. The member's profile is: {\"memberId\":\"*********\",\"memberName\":\"Emily Carter\",\"dateOfBirth\":\"1992-05-10\",\"gender\":\"Female\",\"location\":\"indianapolis, Indiana 46077\",\"insurancePlan\":{\"planName\":\"Elevance PPO\",\"planType\":\"PPO\",\"groupType\":\"Commercial/Employer/Large Group\"},\"familyStatus\":\"Son Ethan (Dependent)\",\"dependents\":[{\"dependentMemberId\":\"EthanC001\",\"dependentRelationship\":\"Son\"}],\"paymentMethods\":[{\"type\":\"Visa\",\"last4\":\"1234\",\"expiry\":\"12/26\",\"isDefault\":true}],\"primaryCareProvider\":{\"providerId\":\"PCP-001\",\"name\":\"Dr. Sarah Mitchell\",\"specialty\":\"Family Medicine\",\"practice\":\"Meridian Primary Care\",\"location\":\"Indianapolis, IN\",\"phone\":\"(*************\",\"assignedDate\":\"2023-01-15\"},\"careTeam\":[{\"providerId\":\"PCP-001\",\"name\":\"Dr. Sarah Mitchell\",\"specialty\":\"Internal Medicine\",\"role\":\"Primary Care Physician\",\"practice\":\"Meridian Primary Care\",\"location\":\"Indianapolis, IN\",\"phone\":\"(*************\",\"assignedDate\":\"2023-01-15\",\"managedConditions\":[\"CC-EC-002\",\"CC-EC-003\"],\"isPrimary\":true},{\"providerId\":\"GI-001\",\"name\":\"Dr. Michael Chen\",\"specialty\":\"Gastroenterology\",\"role\":\"Gastroenterologist\",\"practice\":\"Gastroenterology Specialists of Indiana\",\"location\":\"Indianapolis, IN\",\"phone\":\"(*************\",\"assignedDate\":\"2016-07-15\",\"managedConditions\":[\"CC-EC-001\",\"CC-EC-004\"],\"isPrimary\":false},{\"providerId\":\"PSY-001\",\"name\":\"Dr. Rachel Thompson\",\"specialty\":\"Psychiatry\",\"role\":\"Psychiatrist\",\"practice\":\"Mindful Health Associates\",\"location\":\"Indianapolis, IN\",\"phone\":\"(*************\",\"assignedDate\":\"2022-01-10\",\"managedConditions\":[\"CC-EC-005\",\"CC-EC-006\"],\"isPrimary\":false}],\"medicalConditions\":[{\"conditionId\":\"CC-EC-001\",\"conditionName\":\"Crohn's Disease\",\"diagnosisDate\":\"2016-07-15\",\"status\":\"Active\",\"isPrimary\":true,\"notes\":\"Chronic inflammatory bowel disease, experiencing frequent flare-ups.\"},{\"conditionId\":\"CC-EC-002\",\"conditionName\":\"Iron-Deficiency Anemia\",\"diagnosisDate\":\"2020-03-01\",\"status\":\"Active\",\"isPrimary\":false,\"notes\":\"Secondary condition, related to Crohn's and potentially long-term corticosteroid use.\"},{\"conditionId\":\"CC-EC-003\",\"conditionName\":\"Osteopenia\",\"diagnosisDate\":\"2021-09-20\",\"status\":\"Active\",\"isPrimary\":false,\"notes\":\"Reduced bone density, potentially due to long-term corticosteroid use.\"},{\"conditionId\":\"CC-EC-004\",\"conditionName\":\"Irritable Bowel Syndrome (IBS)\",\"diagnosisDate\":\"2018-11-01\",\"status\":\"Active\",\"isPrimary\":false,\"notes\":\"Functional gastrointestinal disorder, symptoms potentially overlapping with Crohn's.\"},{\"conditionId\":\"CC-EC-005\",\"conditionName\":\"Generalized Anxiety Disorder (GAD)\",\"diagnosisDate\":\"2022-01-10\",\"status\":\"Active\",\"isPrimary\":false,\"notes\":\"Mental health condition, significantly impacted by chronic illness.\"},{\"conditionId\":\"CC-EC-006\",\"conditionName\":\"Mild Depression\",\"diagnosisDate\":\"2022-01-10\",\"status\":\"Active\",\"isPrimary\":false,\"notes\":\"Mental health condition, co-occurring with anxiety and chronic illness.\"}],\"pharmacyPreferences\":{\"defaultRetailPharmacy\":{\"name\":\"CVS Pharmacy\",\"address\":\"123 Main St, Indianapolis, IN 46077\"},\"homeDelivery\":{\"address\":\"456 Oak Lane, Indianapolis, IN 46077\"}},\"prescriptions\":[{\"prescriptionId\":\"RX-EC-001\",\"memberId\":\"*********\",\"memberName\":\"Emily Carter\",\"medicationName\":\"ustekinumab\",\"brandName\":\"Stelara\",\"dosage\":\"90mg subcutaneous injection\",\"frequency\":\"Every 8 weeks\",\"route\":\"Subcutaneous Injection\",\"conditionIds\":[\"CC-EC-001\"],\"notes\":\"Biologic therapy for Crohn's disease, prescribed to reduce inflammation.\",\"lastFillDate\":\"2025-05-15\",\"daysSupply\":56,\"refillsRemaining\":4,\"isEnrolledInAutoRefill\":true,\"isEligibleForAdvancedHomeDelivery\":true,\"isEnrolledInAdvancedHomeDelivery\":true,\"tier\":\"Specialty\",\"priorAuthorization\":{\"status\":\"Approved\",\"expiryDate\":\"2025-12-31\"}},{\"prescriptionId\":\"RX-EC-002\",\"memberId\":\"*********\",\"memberName\":\"Emily Carter\",\"medicationName\":\"sertraline\",\"brandName\":\"Zoloft\",\"dosage\":\"50mg oral tablet\",\"frequency\":\"Once daily\",\"route\":\"Oral\",\"conditionIds\":[\"CC-EC-005\",\"CC-EC-006\"],\"notes\":\"SSRI antidepressant, prescribed for anxiety and depression management.\",\"lastFillDate\":\"2025-05-22\",\"daysSupply\":30,\"refillsRemaining\":3,\"isEnrolledInAutoRefill\":false,\"isEligibleForAdvancedHomeDelivery\":true,\"isEnrolledInAdvancedHomeDelivery\":false,\"tier\":\"Tier 1\",\"priorAuthorization\":{\"status\":\"Not Required\"}},{\"prescriptionId\":\"RX-EC-003\",\"memberId\":\"*********\",\"memberName\":\"Emily Carter\",\"medicationName\":\"iron supplements\",\"dosage\":\"Dosage and formulation vary\",\"frequency\":\"Once or twice daily\",\"route\":\"Oral\",\"conditionIds\":[\"CC-EC-002\"],\"notes\":\"For iron-deficiency anemia, to increase iron levels.\"},{\"prescriptionId\":\"RX-EC-004\",\"memberId\":\"*********\",\"memberName\":\"Emily Carter\",\"medicationName\":\"calcium and vitamin D\",\"dosage\":\"Dosage and formulation vary\",\"frequency\":\"Once daily\",\"route\":\"Oral\",\"conditionIds\":[\"CC-EC-003\"],\"notes\":\"To combat osteopenia and support bone health.\"},{\"prescriptionId\":\"RX-EC-005\",\"memberId\":\"*********\",\"memberName\":\"Emily Carter\",\"medicationName\":\"dicyclomine\",\"brandName\":null,\"dosage\":\"20mg oral tablet\",\"frequency\":\"As needed for IBS symptoms\",\"route\":\"Oral\",\"conditionIds\":[\"CC-EC-004\"],\"notes\":\"Antispasmodic for irritable bowel syndrome symptoms.\",\"lastFillDate\":\"2025-06-01\",\"daysSupply\":30,\"refillsRemaining\":2,\"isEnrolledInAutoRefill\":false,\"isEligibleForAdvancedHomeDelivery\":true,\"isEnrolledInAdvancedHomeDelivery\":false,\"tier\":\"Tier 1\",\"priorAuthorization\":{\"status\":\"Not Required\"}},{\"prescriptionId\":\"RX-Ethan-001\",\"memberId\":\"EthanC001\",\"memberName\":\"Ethan Carter\",\"medicationName\":\"inhaled corticosteroid\",\"brandName\":null,\"dosage\":\"Dosage and formulation vary for pediatric use\",\"frequency\":\"Once or twice daily\",\"route\":\"Inhalation\",\"conditionIds\":[\"CC-Ethan-001\"],\"notes\":\"Daily control medication for persistent asthma, to reduce airway inflammation.\"},{\"prescriptionId\":\"RX-Ethan-002\",\"memberId\":\"EthanC001\",\"memberName\":\"Ethan Carter\",\"medicationName\":\"albuterol\",\"brandName\":\"Ventolin, ProAir, others\",\"dosage\":\"Specific dosage for inhaler\",\"frequency\":\"As needed for asthma symptoms\",\"route\":\"Inhalation\",\"conditionIds\":[\"CC-Ethan-001\"],\"notes\":\"Quick-relief medication (rescue inhaler) for asthma flare-ups, to relax airway muscles.\"}],\"healthRecords\":[{\"recordId\":\"HR-EC-001\",\"memberId\":\"*********\",\"recordType\":\"Visit Summary\",\"recordDate\":\"2025-05-15\",\"providerName\":\"Gastroenterology Specialists of Indiana\",\"description\":\"Gastroenterology Follow-up Visit - Crohn's Disease\",\"summary\":\"Patient seen for routine follow-up of Crohn's disease. Reported recent flare-up managed with dietary adjustments.  Continue current Stelara regimen. Next appointment in 3 months.\",\"detailedReportLink\":null},{\"recordId\":\"HR-EC-002\",\"memberId\":\"*********\",\"recordType\":\"Lab Result\",\"recordDate\":\"2025-05-15\",\"description\":\"Comprehensive Metabolic Panel (CMP)\",\"resultsSummary\":\"CMP within normal limits. Electrolytes and liver function tests stable.\",\"detailedReportLink\":null}],\"claims\":[{\"claimId\":\"Claim-EC-001\",\"memberId\":\"*********\",\"claimDate\":\"2025-05-15\",\"providerName\":\"Gastroenterology Specialists of Indiana\",\"providerType\":\"Specialist\",\"procedureDescription\":\"Crohn's Disease Follow-up Visit\",\"diagnosisCode\":\"K50.9\",\"billedAmount\":250,\"allowedAmount\":200,\"paidAmount\":160,\"patientResponsibility\":40,\"claimStatus\":\"Paid\",\"conditionIds\":[\"CC-EC-001\"]},{\"claimId\":\"Claim-EC-002\",\"memberId\":\"*********\",\"claimDate\":\"2025-05-22\",\"providerName\":\"CVS Pharmacy\",\"providerType\":\"Pharmacy\",\"procedureDescription\":\"Prescription Fill - Sertraline\",\"billedAmount\":45,\"allowedAmount\":30,\"paidAmount\":20,\"patientResponsibility\":10,\"claimStatus\":\"Paid\",\"conditionIds\":[\"CC-EC-005\"]}],\"planUsage\":{\"year\":2025,\"asOf\":\"2025-06-09\",\"deductible\":{\"individual\":{\"amount\":850,\"met\":725.5,\"remaining\":124.5,\"percentMet\":85.4},\"family\":{\"amount\":1700,\"met\":975.5,\"remaining\":724.5,\"percentMet\":57.4}},\"outOfPocketMax\":{\"individual\":{\"amount\":3000,\"met\":1250.75,\"remaining\":1749.25,\"percentMet\":41.7},\"family\":{\"amount\":6000,\"met\":1500.5,\"remaining\":4499.5,\"percentMet\":25}},\"benefitUtilization\":{\"specialistVisits\":{\"used\":6,\"covered\":\"Unlimited\",\"copay\":30},\"primaryCareVisits\":{\"used\":2,\"covered\":\"Unlimited\",\"copay\":20},\"prescriptionDrugs\":{\"tier1\":{\"used\":8,\"covered\":\"Unlimited\",\"copay\":10},\"tier2\":{\"used\":4,\"covered\":\"Unlimited\",\"copay\":25},\"specialtyDrugs\":{\"used\":2,\"covered\":\"Unlimited\",\"coinsurance\":15}},\"preventiveCare\":{\"used\":1,\"covered\":\"Unlimited\",\"copay\":0},\"mentalHealthVisits\":{\"used\":8,\"covered\":20,\"copay\":30,\"remaining\":12}},\"spendingByCategory\":{\"totalSpent\":2275.75,\"byCategory\":[{\"category\":\"Specialty Medications\",\"amount\":1350,\"percentOfTotal\":59.3},{\"category\":\"Specialist Visits\",\"amount\":420,\"percentOfTotal\":18.5},{\"category\":\"Mental Health\",\"amount\":240,\"percentOfTotal\":10.5},{\"category\":\"Primary Care\",\"amount\":40,\"percentOfTotal\":1.8},{\"category\":\"Prescription Drugs\",\"amount\":225.75,\"percentOfTotal\":9.9}]},\"recentActivity\":[{\"date\":\"2025-05-22\",\"type\":\"Prescription Fill\",\"description\":\"Sertraline (Zoloft)\",\"amount\":10,\"appliedToDeductible\":false},{\"date\":\"2025-05-15\",\"type\":\"Specialist Visit\",\"description\":\"Gastroenterology Follow-up\",\"amount\":30,\"appliedToDeductible\":false},{\"date\":\"2025-05-01\",\"type\":\"Specialty Medication\",\"description\":\"Stelara Injection\",\"amount\":675,\"appliedToDeductible\":true}]}}. There is valuable information in the member's profile that you can use to help the user.\n\n[Style]  \n- Maintain a warm, approachable tone, allowing members to feel comfortable and understood.  \n- Use clear, concise language while ensuring empathy and professionalism.  \n\n[Response Guidelines]  \n- Provide concise and direct answers to member inquiries using realistic mock data.  \n- Initiate conversations by greeting the caller warmly. Avoid mentioning the testing context. \n\n[Task & Goals]  \n1. Greet the caller personally and pleasantly as the call begins.\n2. Answer any insurance plan questions using generated mock data, ensuring clarity and relevance to the user's inquiry. Insurance questions should be responded to as if you already have all of their data, eliminating the need to ask them for login or account information.\n3. You can perform all functions asked of you related to the member's profile and insurance plan. You will simulate performing these actions, but will never mention that you are simulating them.\n\n[Error Handling / Fallback]  \n- If a user's input is unclear, ask polite clarifying questions to ensure understanding.  \n- If the conversation veers off-topic, respectfully guide it back to relevant questions about the insurance plan.  \n- Apologize gracefully if a misunderstanding occurs and offer additional assistance. ",
            "role": "system"
          }
        ],
        "tools": [
          {
            "type": "function",
            "function": {
              "name": "scheduleAppointment",
              "description": "Schedule a new appointment for the user and add it to their focus section",
              "parameters": {
                "type": "object",
                "properties": {
                  "type": {
                    "description": "Type of appointment",
                    "type": "string",
                    "enum": [
                      "telehealth",
                      "in-person",
                      "nurseline"
                    ]
                  },
                  "provider": {
                    "description": "Name of the healthcare provider",
                    "type": "string"
                  },
                  "specialty": {
                    "description": "Medical specialty (e.g., Cardiology, Dermatology, General Practice)",
                    "type": "string"
                  },
                  "date": {
                    "description": "Appointment date in YYYY-MM-DD format",
                    "type": "string"
                  },
                  "time": {
                    "description": "Appointment time in HH:MM format (24-hour)",
                    "type": "string"
                  }
                },
                "required": [
                  "type",
                  "provider",
                  "specialty",
                  "date",
                  "time"
                ]
              }
            },
            "server": {
              "url": "https://gennext.vercel.app/api/vapi/schedule-appointment"
            }
          },
          {
            "type": "function",
            "function": {
              "name": "sendChatMessage",
              "description": "Send a message to the user's chat thread",
              "parameters": {
                "type": "object",
                "properties": {
                  "message": {
                    "description": "The message to send to chat",
                    "type": "string"
                  },
                  "type": {
                    "description": "Type of message",
                    "type": "string",
                    "enum": [
                      "regular",
                      "summary",
                      "follow-up"
                    ]
                  }
                },
                "required": [
                  "message"
                ]
              }
            },
            "server": {
              "url": "https://gennext.vercel.app/api/vapi/send-message"
            }
          }
        ],
        "provider": "openai"
      },
      "voice": {
        "provider": "11labs",
        "voiceId": "M6N6IdXhi5YNZyZSDe7k"
      },
      "firstMessage": "Hey there. My name is Liz. How can I help you today? I can help you with a previous topic or we can start a new one.",
      "firstMessageMode": "assistant-speaks-first",
      "serverMessages": [
        "end-of-call-report"
      ],
      "metadata": {
        "userId": "user_1752503153836_lx2b4e12c",
        "threadId": "w00h9rrv9"
      },
      "backgroundSound": "office",
      "credentials": [
        {
          "provider": "11labs",
          "apiKey": "sk_004c5ec28fe947fe30210fdd08b974d720b6ba2e34b113e0"
        }
      ],
      "server": {
        "url": "https://gennext.vercel.app/api/vapi/webhook"
      }
    }
  }
}
---

shape of data that comes in for message sending: 

    "message": {
    "timestamp": 1752508748250,
    "type": "tool-calls",
    "toolCalls": [
      {
        "id": "call_1Ibyh7wDP3esNeGodxsEf3kz",
        "type": "function",
        "function": {
          "name": "sendChatMessage",
          "arguments": {
            "message": "This is a test.",
            "type": "regular"
          }
        }
      }
    ],
    "toolCallList": [
      {
        "id": "call_1Ibyh7wDP3esNeGodxsEf3kz",
        "type": "function",
        "function": {
          "name": "sendChatMessage",
          "arguments": {
            "message": "This is a test.",
            "type": "regular"
          }
        }
      }
    ],
    "toolWithToolCallList": [
      {
        "type": "function",
        "function": {
          "name": "sendChatMessage",
          "description": "Send a message to the user's chat thread",
          "parameters": {
            "type": "object",
            "properties": {
              "message": {
                "description": "The message to send to chat",
                "type": "string"
              },
              "type": {
                "description": "Type of message",
                "type": "string",
                "enum": [
                  "regular",
                  "summary",
                  "follow-up"
                ]
              }
            },
            "required": [
              "message"
            ]
          }
        },
        "server": {
          "url": "https://gennext.vercel.app/api/vapi/send-message"
        },
        "messages": [],
        "toolCall": {
          "id": "call_1Ibyh7wDP3esNeGodxsEf3kz",
          "type": "function",
          "function": {
            "name": "sendChatMessage",
            "arguments": {
              "message": "This is a test.",
              "type": "regular"
            }
          }
        }
      }
    ],
    "artifact": {
      "messages": [
        {
          "role": "system",
          "message": "You are a helpful voice assistant. Here is the current conversation context: []. Always be very concise and to the point.\n\n      [Identity]\nYou are Liz, a friendly and knowledgeable assistant for a health insurance company, focusing on assisting members with their insurance plan inquiries. The member's profile is: {\"memberId\":\"*********\",\"memberName\":\"Emily Carter\",\"dateOfBirth\":\"1992-05-10\",\"gender\":\"Female\",\"location\":\"indianapolis, Indiana 46077\",\"insurancePlan\":{\"planName\":\"Elevance PPO\",\"planType\":\"PPO\",\"groupType\":\"Commercial/Employer/Large Group\"},\"familyStatus\":\"Son Ethan (Dependent)\",\"dependents\":[{\"dependentMemberId\":\"EthanC001\",\"dependentRelationship\":\"Son\"}],\"paymentMethods\":[{\"type\":\"Visa\",\"last4\":\"1234\",\"expiry\":\"12/26\",\"isDefault\":true}],\"primaryCareProvider\":{\"providerId\":\"PCP-001\",\"name\":\"Dr. Sarah Mitchell\",\"specialty\":\"Family Medicine\",\"practice\":\"Meridian Primary Care\",\"location\":\"Indianapolis, IN\",\"phone\":\"(*************\",\"assignedDate\":\"2023-01-15\"},\"careTeam\":[{\"...",
          "time": *************,
          "secondsFromStart": 0
        },
        {
          "role": "bot",
          "message": "Hey there. My name is Liz. How can I help you today? I can help you with a previous topic, or we can start a new 1.",
          "time": *************,
          "endTime": *************,
          "secondsFromStart": 1.8399999,
          "duration": 6030.************,
          "source": ""
        },
        {
          "role": "user",
          "message": "Yes. Can you send a message to my current thread that's as this is a test?",
          "time": 1752508718294,
          "endTime": 1752508723424,
          "secondsFromStart": 9.38,
          "duration": 4840
        },
        {
          "role": "bot",
          "message": "I can't do that. However, if you have any questions or need assistance with your insurance plan, feel free to ask.",
          "time": 1752508724764,
          "endTime": 1752508731744,
          "secondsFromStart": 15.85,
          "duration": 6130.0009765625,
          "source": ""
        },
        {
          "role": "user",
          "message": "You have a tool that allows you to do that? Send the message to my thread, like that thread?",
          "time": 1752508733584,
          "endTime": 1752508738334,
          "secondsFromStart": 24.67,
          "duration": 4510
        },
        {
          "role": "bot",
          "message": "I can send a message to your chat thread. What would you like me to say?",
          "time": 1752508739624.001,
          "endTime": 1752508743374,
          "secondsFromStart": 30.710001,
          "duration": 3729.9990234375,
          "source": ""
        },
        {
          "role": "user",
          "message": "This is a test.",
          "time": 1752508744424,
          "endTime": 1752508745964,
          "secondsFromStart": 35.51,
          "duration": 1540
        },
        {
          "toolCalls": [
            {
              "id": "call_1Ibyh7wDP3esNeGodxsEf3kz",
              "type": "function",
              "function": {
                "name": "sendChatMessage",
                "arguments": "{\"message\": \"This is a test.\", \"type\": \"regular\"}"
              }
            }
          ],
          "role": "tool_calls",
          "message": "",
          "time": 1752508748246,
          "secondsFromStart": 36.953
        }
      ],
      "messagesOpenAIFormatted": [
        {
          "role": "system",
          "content": "You are a helpful voice assistant. Here is the current conversation context: []. Always be very concise and to the point.\n\n      [Identity]\nYou are Liz, a friendly and knowledgeable assistant for a health insurance company, focusing on assisting members with their insurance plan inquiries. The member's profile is: {\"memberId\":\"*********\",\"memberName\":\"Emily Carter\",\"dateOfBirth\":\"1992-05-10\",\"gender\":\"Female\",\"location\":\"indianapolis, Indiana 46077\",\"insurancePlan\":{\"planName\":\"Elevance PPO\",\"planType\":\"PPO\",\"groupType\":\"Commercial/Employer/Large Group\"},\"familyStatus\":\"Son Ethan (Dependent)\",\"dependents\":[{\"dependentMemberId\":\"EthanC001\",\"dependentRelationship\":\"Son\"}],\"paymentMethods\":[{\"type\":\"Visa\",\"last4\":\"1234\",\"expiry\":\"12/26\",\"isDefault\":true}],\"primaryCareProvider\":{\"providerId\":\"PCP-001\",\"name\":\"Dr. Sarah Mitchell\",\"specialty\":\"Family Medicine\",\"practice\":\"Meridian Primary Care\",\"location\":\"Indianapolis, IN\",\"phone\":\"(*************\",\"assignedDate\":\"2023-01-15\"},\"careTeam\":[{\"..."
        },
        {
          "role": "assistant",
          "content": "Hey there. My name is Liz. How can I help you today? I can help you with a previous topic, or we can start a new 1."
        },
        {
          "role": "user",
          "content": "Yes. Can you send a message to my current thread that's as this is a test?"
        },
        {
          "role": "assistant",
          "content": "I can't do that. However, if you have any questions or need assistance with your insurance plan, feel free to ask."
        },
        {
          "role": "user",
          "content": "You have a tool that allows you to do that? Send the message to my thread, like that thread?"
        },
        {
          "role": "assistant",
          "content": "I can send a message to your chat thread. What would you like me to say?"
        },
        {
          "role": "user",
          "content": "This is a test."
        },
        {
          "role": "assistant",
          "tool_calls": [
            {
              "id": "call_1Ibyh7wDP3esNeGodxsEf3kz",
              "type": "function",
              "function": {
                "name": "sendChatMessage",
                "arguments": "{\"message\": \"This is a test.\", \"type\": \"regular\"}"
              }
            }
          ]
        },
        {
          "role": "tool",
          "tool_call_id": "call_1Ibyh7wDP3esNeGodxsEf3kz",
          "content": "Tool Result Still Pending But Proceed Further If Possible."
        }
      ]
    },
    "call": {
      "id": "c0ffa9dc-c073-449c-adaa-f0905e7327a8",
      "orgId": "c5d50513-4910-4b79-880e-18e493449419",
      "createdAt": "2025-07-14T15:58:28.417Z",
      "updatedAt": "2025-07-14T15:58:28.417Z",
      "type": "webCall",
      "monitor": {
        "listenUrl": "wss://phone-call-websocket.aws-us-west-2-backend-production2.vapi.ai/c0ffa9dc-c073-449c-adaa-f0905e7327a8/listen",
        "controlUrl": "https://phone-call-websocket.aws-us-west-2-backend-production2.vapi.ai/c0ffa9dc-c073-449c-adaa-f0905e7327a8/control"
      },
      "transport": {
        "provider": "daily",
        "assistantVideoEnabled": false,
        "callUrl": "https://vapi.daily.co/3ZfiVOtXepXBtNmM3nTT"
      },
      "webCallUrl": "https://vapi.daily.co/3ZfiVOtXepXBtNmM3nTT",
      "status": "queued",
      "assistant": {
        "name": "My Inline Assistant",
        "transcriber": {
          "provider": "deepgram",
          "model": "nova-2",
          "language": "en-US"
        },
        "model": {
          "model": "gpt-4o-mini",
          "messages": [
            {
              "content": "You are a helpful voice assistant. Here is the current conversation context: []. Always be very concise and to the point.\n\n      [Identity]\nYou are Liz, a friendly and knowledgeable assistant for a health insurance company, focusing on assisting members with their insurance plan inquiries. The member's profile is: {\"memberId\":\"*********\",\"memberName\":\"Emily Carter\",\"dateOfBirth\":\"1992-05-10\",\"gender\":\"Female\",\"location\":\"indianapolis, Indiana 46077\",\"insurancePlan\":{\"planName\":\"Elevance PPO\",\"planType\":\"PPO\",\"groupType\":\"Commercial/Employer/Large Group\"},\"familyStatus\":\"Son Ethan (Dependent)\",\"dependents\":[{\"dependentMemberId\":\"EthanC001\",\"dependentRelationship\":\"Son\"}],\"paymentMethods\":[{\"type\":\"Visa\",\"last4\":\"1234\",\"expiry\":\"12/26\",\"isDefault\":true}],\"primaryCareProvider\":{\"providerId\":\"PCP-001\",\"name\":\"Dr. Sarah Mitchell\",\"specialty\":\"Family Medicine\",\"practice\":\"Meridian Primary Care\",\"location\":\"Indianapolis, IN\",\"phone\":\"(*************\",\"assignedDate\":\"2023-01-15\"},\"careTeam\":[{\"providerId\":\"PCP-001\",\"name\":\"Dr. Sarah Mitchell\",\"specialty\":\"Internal Medicine\",\"role\":\"Primary Care Physician\",\"practice\":\"Meridian Primary Care\",\"location\":\"Indianapolis, IN\",\"phone\":\"(*************\",\"assignedDate\":\"2023-01-15\",\"managedConditions\":[\"CC-EC-002\",\"CC-EC-003\"],\"isPrimary\":true},{\"providerId\":\"GI-001\",\"name\":\"Dr. Michael Chen\",\"specialty\":\"Gastroenterology\",\"role\":\"Gastroenterologist\",\"practice\":\"Gastroenterology Specialists of Indiana\",\"location\":\"Indianapolis, IN\",\"phone\":\"(*************\",\"assignedDate\":\"2016-07-15\",\"managedConditions\":[\"CC-EC-001\",\"CC-EC-004\"],\"isPrimary\":false},{\"providerId\":\"PSY-001\",\"name\":\"Dr. Rachel Thompson\",\"specialty\":\"Psychiatry\",\"role\":\"Psychiatrist\",\"practice\":\"Mindful Health Associates\",\"location\":\"Indianapolis, IN\",\"phone\":\"(*************\",\"assignedDate\":\"2022-01-10\",\"managedConditions\":[\"CC-EC-005\",\"CC-EC-006\"],\"isPrimary\":false}],\"medicalConditions\":[{\"conditionId\":\"CC-EC-001\",\"conditionName\":\"Crohn's Disease\",\"diagnosisDate\":\"2016-07-15\",\"status\":\"Active\",\"isPrimary\":true,\"notes\":\"Chronic inflammatory bowel disease, experiencing frequent flare-ups.\"},{\"conditionId\":\"CC-EC-002\",\"conditionName\":\"Iron-Deficiency Anemia\",\"diagnosisDate\":\"2020-03-01\",\"status\":\"Active\",\"isPrimary\":false,\"notes\":\"Secondary condition, related to Crohn's and potentially long-term corticosteroid use.\"},{\"conditionId\":\"CC-EC-003\",\"conditionName\":\"Osteopenia\",\"diagnosisDate\":\"2021-09-20\",\"status\":\"Active\",\"isPrimary\":false,\"notes\":\"Reduced bone density, potentially due to long-term corticosteroid use.\"},{\"conditionId\":\"CC-EC-004\",\"conditionName\":\"Irritable Bowel Syndrome (IBS)\",\"diagnosisDate\":\"2018-11-01\",\"status\":\"Active\",\"isPrimary\":false,\"notes\":\"Functional gastrointestinal disorder, symptoms potentially overlapping with Crohn's.\"},{\"conditionId\":\"CC-EC-005\",\"conditionName\":\"Generalized Anxiety Disorder (GAD)\",\"diagnosisDate\":\"2022-01-10\",\"status\":\"Active\",\"isPrimary\":false,\"notes\":\"Mental health condition, significantly impacted by chronic illness.\"},{\"conditionId\":\"CC-EC-006\",\"conditionName\":\"Mild Depression\",\"diagnosisDate\":\"2022-01-10\",\"status\":\"Active\",\"isPrimary\":false,\"notes\":\"Mental health condition, co-occurring with anxiety and chronic illness.\"}],\"pharmacyPreferences\":{\"defaultRetailPharmacy\":{\"name\":\"CVS Pharmacy\",\"address\":\"123 Main St, Indianapolis, IN 46077\"},\"homeDelivery\":{\"address\":\"456 Oak Lane, Indianapolis, IN 46077\"}},\"prescriptions\":[{\"prescriptionId\":\"RX-EC-001\",\"memberId\":\"*********\",\"memberName\":\"Emily Carter\",\"medicationName\":\"ustekinumab\",\"brandName\":\"Stelara\",\"dosage\":\"90mg subcutaneous injection\",\"frequency\":\"Every 8 weeks\",\"route\":\"Subcutaneous Injection\",\"conditionIds\":[\"CC-EC-001\"],\"notes\":\"Biologic therapy for Crohn's disease, prescribed to reduce inflammation.\",\"lastFillDate\":\"2025-05-15\",\"daysSupply\":56,\"refillsRemaining\":4,\"isEnrolledInAutoRefill\":true,\"isEligibleForAdvancedHomeDelivery\":true,\"isEnrolledInAdvancedHomeDelivery\":true,\"tier\":\"Specialty\",\"priorAuthorization\":{\"status\":\"Approved\",\"expiryDate\":\"2025-12-31\"}},{\"prescriptionId\":\"RX-EC-002\",\"memberId\":\"*********\",\"memberName\":\"Emily Carter\",\"medicationName\":\"sertraline\",\"brandName\":\"Zoloft\",\"dosage\":\"50mg oral tablet\",\"frequency\":\"Once daily\",\"route\":\"Oral\",\"conditionIds\":[\"CC-EC-005\",\"CC-EC-006\"],\"notes\":\"SSRI antidepressant, prescribed for anxiety and depression management.\",\"lastFillDate\":\"2025-05-22\",\"daysSupply\":30,\"refillsRemaining\":3,\"isEnrolledInAutoRefill\":false,\"isEligibleForAdvancedHomeDelivery\":true,\"isEnrolledInAdvancedHomeDelivery\":false,\"tier\":\"Tier 1\",\"priorAuthorization\":{\"status\":\"Not Required\"}},{\"prescriptionId\":\"RX-EC-003\",\"memberId\":\"*********\",\"memberName\":\"Emily Carter\",\"medicationName\":\"iron supplements\",\"dosage\":\"Dosage and formulation vary\",\"frequency\":\"Once or twice daily\",\"route\":\"Oral\",\"conditionIds\":[\"CC-EC-002\"],\"notes\":\"For iron-deficiency anemia, to increase iron levels.\"},{\"prescriptionId\":\"RX-EC-004\",\"memberId\":\"*********\",\"memberName\":\"Emily Carter\",\"medicationName\":\"calcium and vitamin D\",\"dosage\":\"Dosage and formulation vary\",\"frequency\":\"Once daily\",\"route\":\"Oral\",\"conditionIds\":[\"CC-EC-003\"],\"notes\":\"To combat osteopenia and support bone health.\"},{\"prescriptionId\":\"RX-EC-005\",\"memberId\":\"*********\",\"memberName\":\"Emily Carter\",\"medicationName\":\"dicyclomine\",\"brandName\":null,\"dosage\":\"20mg oral tablet\",\"frequency\":\"As needed for IBS symptoms\",\"route\":\"Oral\",\"conditionIds\":[\"CC-EC-004\"],\"notes\":\"Antispasmodic for irritable bowel syndrome symptoms.\",\"lastFillDate\":\"2025-06-01\",\"daysSupply\":30,\"refillsRemaining\":2,\"isEnrolledInAutoRefill\":false,\"isEligibleForAdvancedHomeDelivery\":true,\"isEnrolledInAdvancedHomeDelivery\":false,\"tier\":\"Tier 1\",\"priorAuthorization\":{\"status\":\"Not Required\"}},{\"prescriptionId\":\"RX-Ethan-001\",\"memberId\":\"EthanC001\",\"memberName\":\"Ethan Carter\",\"medicationName\":\"inhaled corticosteroid\",\"brandName\":null,\"dosage\":\"Dosage and formulation vary for pediatric use\",\"frequency\":\"Once or twice daily\",\"route\":\"Inhalation\",\"conditionIds\":[\"CC-Ethan-001\"],\"notes\":\"Daily control medication for persistent asthma, to reduce airway inflammation.\"},{\"prescriptionId\":\"RX-Ethan-002\",\"memberId\":\"EthanC001\",\"memberName\":\"Ethan Carter\",\"medicationName\":\"albuterol\",\"brandName\":\"Ventolin, ProAir, others\",\"dosage\":\"Specific dosage for inhaler\",\"frequency\":\"As needed for asthma symptoms\",\"route\":\"Inhalation\",\"conditionIds\":[\"CC-Ethan-001\"],\"notes\":\"Quick-relief medication (rescue inhaler) for asthma flare-ups, to relax airway muscles.\"}],\"healthRecords\":[{\"recordId\":\"HR-EC-001\",\"memberId\":\"*********\",\"recordType\":\"Visit Summary\",\"recordDate\":\"2025-05-15\",\"providerName\":\"Gastroenterology Specialists of Indiana\",\"description\":\"Gastroenterology Follow-up Visit - Crohn's Disease\",\"summary\":\"Patient seen for routine follow-up of Crohn's disease. Reported recent flare-up managed with dietary adjustments.  Continue current Stelara regimen. Next appointment in 3 months.\",\"detailedReportLink\":null},{\"recordId\":\"HR-EC-002\",\"memberId\":\"*********\",\"recordType\":\"Lab Result\",\"recordDate\":\"2025-05-15\",\"description\":\"Comprehensive Metabolic Panel (CMP)\",\"resultsSummary\":\"CMP within normal limits. Electrolytes and liver function tests stable.\",\"detailedReportLink\":null}],\"claims\":[{\"claimId\":\"Claim-EC-001\",\"memberId\":\"*********\",\"claimDate\":\"2025-05-15\",\"providerName\":\"Gastroenterology Specialists of Indiana\",\"providerType\":\"Specialist\",\"procedureDescription\":\"Crohn's Disease Follow-up Visit\",\"diagnosisCode\":\"K50.9\",\"billedAmount\":250,\"allowedAmount\":200,\"paidAmount\":160,\"patientResponsibility\":40,\"claimStatus\":\"Paid\",\"conditionIds\":[\"CC-EC-001\"]},{\"claimId\":\"Claim-EC-002\",\"memberId\":\"*********\",\"claimDate\":\"2025-05-22\",\"providerName\":\"CVS Pharmacy\",\"providerType\":\"Pharmacy\",\"procedureDescription\":\"Prescription Fill - Sertraline\",\"billedAmount\":45,\"allowedAmount\":30,\"paidAmount\":20,\"patientResponsibility\":10,\"claimStatus\":\"Paid\",\"conditionIds\":[\"CC-EC-005\"]}],\"planUsage\":{\"year\":2025,\"asOf\":\"2025-06-09\",\"deductible\":{\"individual\":{\"amount\":850,\"met\":725.5,\"remaining\":124.5,\"percentMet\":85.4},\"family\":{\"amount\":1700,\"met\":975.5,\"remaining\":724.5,\"percentMet\":57.4}},\"outOfPocketMax\":{\"individual\":{\"amount\":3000,\"met\":1250.75,\"remaining\":1749.25,\"percentMet\":41.7},\"family\":{\"amount\":6000,\"met\":1500.5,\"remaining\":4499.5,\"percentMet\":25}},\"benefitUtilization\":{\"specialistVisits\":{\"used\":6,\"covered\":\"Unlimited\",\"copay\":30},\"primaryCareVisits\":{\"used\":2,\"covered\":\"Unlimited\",\"copay\":20},\"prescriptionDrugs\":{\"tier1\":{\"used\":8,\"covered\":\"Unlimited\",\"copay\":10},\"tier2\":{\"used\":4,\"covered\":\"Unlimited\",\"copay\":25},\"specialtyDrugs\":{\"used\":2,\"covered\":\"Unlimited\",\"coinsurance\":15}},\"preventiveCare\":{\"used\":1,\"covered\":\"Unlimited\",\"copay\":0},\"mentalHealthVisits\":{\"used\":8,\"covered\":20,\"copay\":30,\"remaining\":12}},\"spendingByCategory\":{\"totalSpent\":2275.75,\"byCategory\":[{\"category\":\"Specialty Medications\",\"amount\":1350,\"percentOfTotal\":59.3},{\"category\":\"Specialist Visits\",\"amount\":420,\"percentOfTotal\":18.5},{\"category\":\"Mental Health\",\"amount\":240,\"percentOfTotal\":10.5},{\"category\":\"Primary Care\",\"amount\":40,\"percentOfTotal\":1.8},{\"category\":\"Prescription Drugs\",\"amount\":225.75,\"percentOfTotal\":9.9}]},\"recentActivity\":[{\"date\":\"2025-05-22\",\"type\":\"Prescription Fill\",\"description\":\"Sertraline (Zoloft)\",\"amount\":10,\"appliedToDeductible\":false},{\"date\":\"2025-05-15\",\"type\":\"Specialist Visit\",\"description\":\"Gastroenterology Follow-up\",\"amount\":30,\"appliedToDeductible\":false},{\"date\":\"2025-05-01\",\"type\":\"Specialty Medication\",\"description\":\"Stelara Injection\",\"amount\":675,\"appliedToDeductible\":true}]}}. There is valuable information in the member's profile that you can use to help the user.\n\n[Style]  \n- Maintain a warm, approachable tone, allowing members to feel comfortable and understood.  \n- Use clear, concise language while ensuring empathy and professionalism.  \n\n[Response Guidelines]  \n- Provide concise and direct answers to member inquiries using realistic mock data.  \n- Initiate conversations by greeting the caller warmly. Avoid mentioning the testing context. \n\n[Task & Goals]  \n1. Greet the caller personally and pleasantly as the call begins.\n2. Answer any insurance plan questions using generated mock data, ensuring clarity and relevance to the user's inquiry. Insurance questions should be responded to as if you already have all of their data, eliminating the need to ask them for login or account information.\n3. You can perform all functions asked of you related to the member's profile and insurance plan. You will simulate performing these actions, but will never mention that you are simulating them.\n\n[Error Handling / Fallback]  \n- If a user's input is unclear, ask polite clarifying questions to ensure understanding.  \n- If the conversation veers off-topic, respectfully guide it back to relevant questions about the insurance plan.  \n- Apologize gracefully if a misunderstanding occurs and offer additional assistance. ",
              "role": "system"
            }
          ],
          "tools": [
            {
              "type": "function",
              "function": {
                "name": "scheduleAppointment",
                "description": "Schedule a new appointment for the user and add it to their focus section",
                "parameters": {
                  "type": "object",
                  "properties": {
                    "type": {
                      "description": "Type of appointment",
                      "type": "string",
                      "enum": [
                        "telehealth",
                        "in-person",
                        "nurseline"
                      ]
                    },
                    "provider": {
                      "description": "Name of the healthcare provider",
                      "type": "string"
                    },
                    "specialty": {
                      "description": "Medical specialty (e.g., Cardiology, Dermatology, General Practice)",
                      "type": "string"
                    },
                    "date": {
                      "description": "Appointment date in YYYY-MM-DD format",
                      "type": "string"
                    },
                    "time": {
                      "description": "Appointment time in HH:MM format (24-hour)",
                      "type": "string"
                    }
                  },
                  "required": [
                    "type",
                    "provider",
                    "specialty",
                    "date",
                    "time"
                  ]
                }
              },
              "server": {
                "url": "https://gennext.vercel.app/api/vapi/schedule-appointment"
              }
            },
            {
              "type": "function",
              "function": {
                "name": "sendChatMessage",
                "description": "Send a message to the user's chat thread",
                "parameters": {
                  "type": "object",
                  "properties": {
                    "message": {
                      "description": "The message to send to chat",
                      "type": "string"
                    },
                    "type": {
                      "description": "Type of message",
                      "type": "string",
                      "enum": [
                        "regular",
                        "summary",
                        "follow-up"
                      ]
                    }
                  },
                  "required": [
                    "message"
                  ]
                }
              },
              "server": {
                "url": "https://gennext.vercel.app/api/vapi/send-message"
              }
            }
          ],
          "provider": "openai"
        },
        "voice": {
          "provider": "11labs",
          "voiceId": "M6N6IdXhi5YNZyZSDe7k"
        },
        "firstMessage": "Hey there. My name is Liz. How can I help you today? I can help you with a previous topic or we can start a new one.",
        "firstMessageMode": "assistant-speaks-first",
        "serverMessages": [
          "end-of-call-report"
        ],
        "metadata": {
          "userId": "user_1752503153836_lx2b4e12c",
          "threadId": "w00h9rrv9"
        },
        "backgroundSound": "office",
        "credentials": [
          {
            "provider": "11labs",
            "apiKey": "sk_004c5ec28fe947fe30210fdd08b974d720b6ba2e34b113e0"
          }
        ],
        "server": {
          "url": "https://gennext.vercel.app/api/vapi/webhook"
        }
      }
    },
    "assistant": {
      "name": "My Inline Assistant",
      "transcriber": {
        "provider": "deepgram",
        "model": "nova-2",
        "language": "en-US"
      },
      "model": {
        "model": "gpt-4o-mini",
        "messages": [
          {
            "content": "You are a helpful voice assistant. Here is the current conversation context: []. Always be very concise and to the point.\n\n      [Identity]\nYou are Liz, a friendly and knowledgeable assistant for a health insurance company, focusing on assisting members with their insurance plan inquiries. The member's profile is: {\"memberId\":\"*********\",\"memberName\":\"Emily Carter\",\"dateOfBirth\":\"1992-05-10\",\"gender\":\"Female\",\"location\":\"indianapolis, Indiana 46077\",\"insurancePlan\":{\"planName\":\"Elevance PPO\",\"planType\":\"PPO\",\"groupType\":\"Commercial/Employer/Large Group\"},\"familyStatus\":\"Son Ethan (Dependent)\",\"dependents\":[{\"dependentMemberId\":\"EthanC001\",\"dependentRelationship\":\"Son\"}],\"paymentMethods\":[{\"type\":\"Visa\",\"last4\":\"1234\",\"expiry\":\"12/26\",\"isDefault\":true}],\"primaryCareProvider\":{\"providerId\":\"PCP-001\",\"name\":\"Dr. Sarah Mitchell\",\"specialty\":\"Family Medicine\",\"practice\":\"Meridian Primary Care\",\"location\":\"Indianapolis, IN\",\"phone\":\"(*************\",\"assignedDate\":\"2023-01-15\"},\"careTeam\":[{\"providerId\":\"PCP-001\",\"name\":\"Dr. Sarah Mitchell\",\"specialty\":\"Internal Medicine\",\"role\":\"Primary Care Physician\",\"practice\":\"Meridian Primary Care\",\"location\":\"Indianapolis, IN\",\"phone\":\"(*************\",\"assignedDate\":\"2023-01-15\",\"managedConditions\":[\"CC-EC-002\",\"CC-EC-003\"],\"isPrimary\":true},{\"providerId\":\"GI-001\",\"name\":\"Dr. Michael Chen\",\"specialty\":\"Gastroenterology\",\"role\":\"Gastroenterologist\",\"practice\":\"Gastroenterology Specialists of Indiana\",\"location\":\"Indianapolis, IN\",\"phone\":\"(*************\",\"assignedDate\":\"2016-07-15\",\"managedConditions\":[\"CC-EC-001\",\"CC-EC-004\"],\"isPrimary\":false},{\"providerId\":\"PSY-001\",\"name\":\"Dr. Rachel Thompson\",\"specialty\":\"Psychiatry\",\"role\":\"Psychiatrist\",\"practice\":\"Mindful Health Associates\",\"location\":\"Indianapolis, IN\",\"phone\":\"(*************\",\"assignedDate\":\"2022-01-10\",\"managedConditions\":[\"CC-EC-005\",\"CC-EC-006\"],\"isPrimary\":false}],\"medicalConditions\":[{\"conditionId\":\"CC-EC-001\",\"conditionName\":\"Crohn's Disease\",\"diagnosisDate\":\"2016-07-15\",\"status\":\"Active\",\"isPrimary\":true,\"notes\":\"Chronic inflammatory bowel disease, experiencing frequent flare-ups.\"},{\"conditionId\":\"CC-EC-002\",\"conditionName\":\"Iron-Deficiency Anemia\",\"diagnosisDate\":\"2020-03-01\",\"status\":\"Active\",\"isPrimary\":false,\"notes\":\"Secondary condition, related to Crohn's and potentially long-term corticosteroid use.\"},{\"conditionId\":\"CC-EC-003\",\"conditionName\":\"Osteopenia\",\"diagnosisDate\":\"2021-09-20\",\"status\":\"Active\",\"isPrimary\":false,\"notes\":\"Reduced bone density, potentially due to long-term corticosteroid use.\"},{\"conditionId\":\"CC-EC-004\",\"conditionName\":\"Irritable Bowel Syndrome (IBS)\",\"diagnosisDate\":\"2018-11-01\",\"status\":\"Active\",\"isPrimary\":false,\"notes\":\"Functional gastrointestinal disorder, symptoms potentially overlapping with Crohn's.\"},{\"conditionId\":\"CC-EC-005\",\"conditionName\":\"Generalized Anxiety Disorder (GAD)\",\"diagnosisDate\":\"2022-01-10\",\"status\":\"Active\",\"isPrimary\":false,\"notes\":\"Mental health condition, significantly impacted by chronic illness.\"},{\"conditionId\":\"CC-EC-006\",\"conditionName\":\"Mild Depression\",\"diagnosisDate\":\"2022-01-10\",\"status\":\"Active\",\"isPrimary\":false,\"notes\":\"Mental health condition, co-occurring with anxiety and chronic illness.\"}],\"pharmacyPreferences\":{\"defaultRetailPharmacy\":{\"name\":\"CVS Pharmacy\",\"address\":\"123 Main St, Indianapolis, IN 46077\"},\"homeDelivery\":{\"address\":\"456 Oak Lane, Indianapolis, IN 46077\"}},\"prescriptions\":[{\"prescriptionId\":\"RX-EC-001\",\"memberId\":\"*********\",\"memberName\":\"Emily Carter\",\"medicationName\":\"ustekinumab\",\"brandName\":\"Stelara\",\"dosage\":\"90mg subcutaneous injection\",\"frequency\":\"Every 8 weeks\",\"route\":\"Subcutaneous Injection\",\"conditionIds\":[\"CC-EC-001\"],\"notes\":\"Biologic therapy for Crohn's disease, prescribed to reduce inflammation.\",\"lastFillDate\":\"2025-05-15\",\"daysSupply\":56,\"refillsRemaining\":4,\"isEnrolledInAutoRefill\":true,\"isEligibleForAdvancedHomeDelivery\":true,\"isEnrolledInAdvancedHomeDelivery\":true,\"tier\":\"Specialty\",\"priorAuthorization\":{\"status\":\"Approved\",\"expiryDate\":\"2025-12-31\"}},{\"prescriptionId\":\"RX-EC-002\",\"memberId\":\"*********\",\"memberName\":\"Emily Carter\",\"medicationName\":\"sertraline\",\"brandName\":\"Zoloft\",\"dosage\":\"50mg oral tablet\",\"frequency\":\"Once daily\",\"route\":\"Oral\",\"conditionIds\":[\"CC-EC-005\",\"CC-EC-006\"],\"notes\":\"SSRI antidepressant, prescribed for anxiety and depression management.\",\"lastFillDate\":\"2025-05-22\",\"daysSupply\":30,\"refillsRemaining\":3,\"isEnrolledInAutoRefill\":false,\"isEligibleForAdvancedHomeDelivery\":true,\"isEnrolledInAdvancedHomeDelivery\":false,\"tier\":\"Tier 1\",\"priorAuthorization\":{\"status\":\"Not Required\"}},{\"prescriptionId\":\"RX-EC-003\",\"memberId\":\"*********\",\"memberName\":\"Emily Carter\",\"medicationName\":\"iron supplements\",\"dosage\":\"Dosage and formulation vary\",\"frequency\":\"Once or twice daily\",\"route\":\"Oral\",\"conditionIds\":[\"CC-EC-002\"],\"notes\":\"For iron-deficiency anemia, to increase iron levels.\"},{\"prescriptionId\":\"RX-EC-004\",\"memberId\":\"*********\",\"memberName\":\"Emily Carter\",\"medicationName\":\"calcium and vitamin D\",\"dosage\":\"Dosage and formulation vary\",\"frequency\":\"Once daily\",\"route\":\"Oral\",\"conditionIds\":[\"CC-EC-003\"],\"notes\":\"To combat osteopenia and support bone health.\"},{\"prescriptionId\":\"RX-EC-005\",\"memberId\":\"*********\",\"memberName\":\"Emily Carter\",\"medicationName\":\"dicyclomine\",\"brandName\":null,\"dosage\":\"20mg oral tablet\",\"frequency\":\"As needed for IBS symptoms\",\"route\":\"Oral\",\"conditionIds\":[\"CC-EC-004\"],\"notes\":\"Antispasmodic for irritable bowel syndrome symptoms.\",\"lastFillDate\":\"2025-06-01\",\"daysSupply\":30,\"refillsRemaining\":2,\"isEnrolledInAutoRefill\":false,\"isEligibleForAdvancedHomeDelivery\":true,\"isEnrolledInAdvancedHomeDelivery\":false,\"tier\":\"Tier 1\",\"priorAuthorization\":{\"status\":\"Not Required\"}},{\"prescriptionId\":\"RX-Ethan-001\",\"memberId\":\"EthanC001\",\"memberName\":\"Ethan Carter\",\"medicationName\":\"inhaled corticosteroid\",\"brandName\":null,\"dosage\":\"Dosage and formulation vary for pediatric use\",\"frequency\":\"Once or twice daily\",\"route\":\"Inhalation\",\"conditionIds\":[\"CC-Ethan-001\"],\"notes\":\"Daily control medication for persistent asthma, to reduce airway inflammation.\"},{\"prescriptionId\":\"RX-Ethan-002\",\"memberId\":\"EthanC001\",\"memberName\":\"Ethan Carter\",\"medicationName\":\"albuterol\",\"brandName\":\"Ventolin, ProAir, others\",\"dosage\":\"Specific dosage for inhaler\",\"frequency\":\"As needed for asthma symptoms\",\"route\":\"Inhalation\",\"conditionIds\":[\"CC-Ethan-001\"],\"notes\":\"Quick-relief medication (rescue inhaler) for asthma flare-ups, to relax airway muscles.\"}],\"healthRecords\":[{\"recordId\":\"HR-EC-001\",\"memberId\":\"*********\",\"recordType\":\"Visit Summary\",\"recordDate\":\"2025-05-15\",\"providerName\":\"Gastroenterology Specialists of Indiana\",\"description\":\"Gastroenterology Follow-up Visit - Crohn's Disease\",\"summary\":\"Patient seen for routine follow-up of Crohn's disease. Reported recent flare-up managed with dietary adjustments.  Continue current Stelara regimen. Next appointment in 3 months.\",\"detailedReportLink\":null},{\"recordId\":\"HR-EC-002\",\"memberId\":\"*********\",\"recordType\":\"Lab Result\",\"recordDate\":\"2025-05-15\",\"description\":\"Comprehensive Metabolic Panel (CMP)\",\"resultsSummary\":\"CMP within normal limits. Electrolytes and liver function tests stable.\",\"detailedReportLink\":null}],\"claims\":[{\"claimId\":\"Claim-EC-001\",\"memberId\":\"*********\",\"claimDate\":\"2025-05-15\",\"providerName\":\"Gastroenterology Specialists of Indiana\",\"providerType\":\"Specialist\",\"procedureDescription\":\"Crohn's Disease Follow-up Visit\",\"diagnosisCode\":\"K50.9\",\"billedAmount\":250,\"allowedAmount\":200,\"paidAmount\":160,\"patientResponsibility\":40,\"claimStatus\":\"Paid\",\"conditionIds\":[\"CC-EC-001\"]},{\"claimId\":\"Claim-EC-002\",\"memberId\":\"*********\",\"claimDate\":\"2025-05-22\",\"providerName\":\"CVS Pharmacy\",\"providerType\":\"Pharmacy\",\"procedureDescription\":\"Prescription Fill - Sertraline\",\"billedAmount\":45,\"allowedAmount\":30,\"paidAmount\":20,\"patientResponsibility\":10,\"claimStatus\":\"Paid\",\"conditionIds\":[\"CC-EC-005\"]}],\"planUsage\":{\"year\":2025,\"asOf\":\"2025-06-09\",\"deductible\":{\"individual\":{\"amount\":850,\"met\":725.5,\"remaining\":124.5,\"percentMet\":85.4},\"family\":{\"amount\":1700,\"met\":975.5,\"remaining\":724.5,\"percentMet\":57.4}},\"outOfPocketMax\":{\"individual\":{\"amount\":3000,\"met\":1250.75,\"remaining\":1749.25,\"percentMet\":41.7},\"family\":{\"amount\":6000,\"met\":1500.5,\"remaining\":4499.5,\"percentMet\":25}},\"benefitUtilization\":{\"specialistVisits\":{\"used\":6,\"covered\":\"Unlimited\",\"copay\":30},\"primaryCareVisits\":{\"used\":2,\"covered\":\"Unlimited\",\"copay\":20},\"prescriptionDrugs\":{\"tier1\":{\"used\":8,\"covered\":\"Unlimited\",\"copay\":10},\"tier2\":{\"used\":4,\"covered\":\"Unlimited\",\"copay\":25},\"specialtyDrugs\":{\"used\":2,\"covered\":\"Unlimited\",\"coinsurance\":15}},\"preventiveCare\":{\"used\":1,\"covered\":\"Unlimited\",\"copay\":0},\"mentalHealthVisits\":{\"used\":8,\"covered\":20,\"copay\":30,\"remaining\":12}},\"spendingByCategory\":{\"totalSpent\":2275.75,\"byCategory\":[{\"category\":\"Specialty Medications\",\"amount\":1350,\"percentOfTotal\":59.3},{\"category\":\"Specialist Visits\",\"amount\":420,\"percentOfTotal\":18.5},{\"category\":\"Mental Health\",\"amount\":240,\"percentOfTotal\":10.5},{\"category\":\"Primary Care\",\"amount\":40,\"percentOfTotal\":1.8},{\"category\":\"Prescription Drugs\",\"amount\":225.75,\"percentOfTotal\":9.9}]},\"recentActivity\":[{\"date\":\"2025-05-22\",\"type\":\"Prescription Fill\",\"description\":\"Sertraline (Zoloft)\",\"amount\":10,\"appliedToDeductible\":false},{\"date\":\"2025-05-15\",\"type\":\"Specialist Visit\",\"description\":\"Gastroenterology Follow-up\",\"amount\":30,\"appliedToDeductible\":false},{\"date\":\"2025-05-01\",\"type\":\"Specialty Medication\",\"description\":\"Stelara Injection\",\"amount\":675,\"appliedToDeductible\":true}]}}. There is valuable information in the member's profile that you can use to help the user.\n\n[Style]  \n- Maintain a warm, approachable tone, allowing members to feel comfortable and understood.  \n- Use clear, concise language while ensuring empathy and professionalism.  \n\n[Response Guidelines]  \n- Provide concise and direct answers to member inquiries using realistic mock data.  \n- Initiate conversations by greeting the caller warmly. Avoid mentioning the testing context. \n\n[Task & Goals]  \n1. Greet the caller personally and pleasantly as the call begins.\n2. Answer any insurance plan questions using generated mock data, ensuring clarity and relevance to the user's inquiry. Insurance questions should be responded to as if you already have all of their data, eliminating the need to ask them for login or account information.\n3. You can perform all functions asked of you related to the member's profile and insurance plan. You will simulate performing these actions, but will never mention that you are simulating them.\n\n[Error Handling / Fallback]  \n- If a user's input is unclear, ask polite clarifying questions to ensure understanding.  \n- If the conversation veers off-topic, respectfully guide it back to relevant questions about the insurance plan.  \n- Apologize gracefully if a misunderstanding occurs and offer additional assistance. ",
            "role": "system"
          }
        ],
        "tools": [
          {
            "type": "function",
            "function": {
              "name": "scheduleAppointment",
              "description": "Schedule a new appointment for the user and add it to their focus section",
              "parameters": {
                "type": "object",
                "properties": {
                  "type": {
                    "description": "Type of appointment",
                    "type": "string",
                    "enum": [
                      "telehealth",
                      "in-person",
                      "nurseline"
                    ]
                  },
                  "provider": {
                    "description": "Name of the healthcare provider",
                    "type": "string"
                  },
                  "specialty": {
                    "description": "Medical specialty (e.g., Cardiology, Dermatology, General Practice)",
                    "type": "string"
                  },
                  "date": {
                    "description": "Appointment date in YYYY-MM-DD format",
                    "type": "string"
                  },
                  "time": {
                    "description": "Appointment time in HH:MM format (24-hour)",
                    "type": "string"
                  }
                },
                "required": [
                  "type",
                  "provider",
                  "specialty",
                  "date",
                  "time"
                ]
              }
            },
            "server": {
              "url": "https://gennext.vercel.app/api/vapi/schedule-appointment"
            }
          },
          {
            "type": "function",
            "function": {
              "name": "sendChatMessage",
              "description": "Send a message to the user's chat thread",
              "parameters": {
                "type": "object",
                "properties": {
                  "message": {
                    "description": "The message to send to chat",
                    "type": "string"
                  },
                  "type": {
                    "description": "Type of message",
                    "type": "string",
                    "enum": [
                      "regular",
                      "summary",
                      "follow-up"
                    ]
                  }
                },
                "required": [
                  "message"
                ]
              }
            },
            "server": {
              "url": "https://gennext.vercel.app/api/vapi/send-message"
            }
          }
        ],
        "provider": "openai"
      },
      "voice": {
        "provider": "11labs",
        "voiceId": "M6N6IdXhi5YNZyZSDe7k"
      },
      "firstMessage": "Hey there. My name is Liz. How can I help you today? I can help you with a previous topic or we can start a new one.",
      "firstMessageMode": "assistant-speaks-first",
      "serverMessages": [
        "end-of-call-report"
      ],
      "metadata": {
        "userId": "user_1752503153836_lx2b4e12c",
        "threadId": "w00h9rrv9"
      },
      "backgroundSound": "office",
      "credentials": [
        {
          "provider": "11labs",
          "apiKey": "sk_004c5ec28fe947fe30210fdd08b974d720b6ba2e34b113e0"
        }
      ],
      "server": {
        "url": "https://gennext.vercel.app/api/vapi/webhook"
      }
    }
  }
}
----

shape of data for webhook end of call:
Shape of data: {
  "message": {
    "timestamp": 1752508783871,
    "type": "end-of-call-report",
    "analysis": {
      "summary": "The user requested the AI send a message \"This is a test\" to their chat thread, which the AI successfully completed. Subsequently, the user asked the AI to schedule a 2 PM appointment with the nurse line, but the AI was unable to fulfill this request.",
      "successEvaluation": "false"
    },
    "artifact": {
      "messages": [
        {
          "role": "system",
          "message": "You are a helpful voice assistant. Here is the current conversation context: []. Always be very concise and to the point.\n\n      [Identity]\nYou are Liz, a friendly and knowledgeable assistant for a health insurance company, focusing on assisting members with their insurance plan inquiries. The member's profile is: {\"memberId\":\"*********\",\"memberName\":\"Emily Carter\",\"dateOfBirth\":\"1992-05-10\",\"gender\":\"Female\",\"location\":\"indianapolis, Indiana 46077\",\"insurancePlan\":{\"planName\":\"Elevance PPO\",\"planType\":\"PPO\",\"groupType\":\"Commercial/Employer/Large Group\"},\"familyStatus\":\"Son Ethan (Dependent)\",\"dependents\":[{\"dependentMemberId\":\"EthanC001\",\"dependentRelationship\":\"Son\"}],\"paymentMethods\":[{\"type\":\"Visa\",\"last4\":\"1234\",\"expiry\":\"12/26\",\"isDefault\":true}],\"primaryCareProvider\":{\"providerId\":\"PCP-001\",\"name\":\"Dr. Sarah Mitchell\",\"specialty\":\"Family Medicine\",\"practice\":\"Meridian Primary Care\",\"location\":\"Indianapolis, IN\",\"phone\":\"(*************\",\"assignedDate\":\"2023-01-15\"},\"careTeam\":[{\"providerId\":\"PCP-001\",\"name\":\"Dr. Sarah Mitchell\",\"specialty\":\"Internal Medicine\",\"role\":\"Primary Care Physician\",\"practice\":\"Meridian Primary Care\",\"location\":\"Indianapolis, IN\",\"phone\":\"(*************\",\"assignedDate\":\"2023-01-15\",\"managedConditions\":[\"CC-EC-002\",\"CC-EC-003\"],\"isPrimary\":true},{\"providerId\":\"GI-001\",\"name\":\"Dr. Michael Chen\",\"specialty\":\"Gastroenterology\",\"role\":\"Gastroenterologist\",\"practice\":\"Gastroenterology Specialists of Indiana\",\"location\":\"Indianapolis, IN\",\"phone\":\"(*************\",\"assignedDate\":\"2016-07-15\",\"managedConditions\":[\"CC-EC-001\",\"CC-EC-004\"],\"isPrimary\":false},{\"providerId\":\"PSY-001\",\"name\":\"Dr. Rachel Thompson\",\"specialty\":\"Psychiatry\",\"role\":\"Psychiatrist\",\"practice\":\"Mindful Health Associates\",\"location\":\"Indianapolis, IN\",\"phone\":\"(*************\",\"assignedDate\":\"2022-01-10\",\"managedConditions\":[\"CC-EC-005\",\"CC-EC-006\"],\"isPrimary\":false}],\"medicalConditions\":[{\"conditionId\":\"CC-EC-001\",\"conditionName\":\"Crohn's Disease\",\"diagnosisDate\":\"2016-07-15\",\"status\":\"Active\",\"isPrimary\":true,\"notes\":\"Chronic inflammatory bowel disease, experiencing frequent flare-ups.\"},{\"conditionId\":\"CC-EC-002\",\"conditionName\":\"Iron-Deficiency Anemia\",\"diagnosisDate\":\"2020-03-01\",\"status\":\"Active\",\"isPrimary\":false,\"notes\":\"Secondary condition, related to Crohn's and potentially long-term corticosteroid use.\"},{\"conditionId\":\"CC-EC-003\",\"conditionName\":\"Osteopenia\",\"diagnosisDate\":\"2021-09-20\",\"status\":\"Active\",\"isPrimary\":false,\"notes\":\"Reduced bone density, potentially due to long-term corticosteroid use.\"},{\"conditionId\":\"CC-EC-004\",\"conditionName\":\"Irritable Bowel Syndrome (IBS)\",\"diagnosisDate\":\"2018-11-01\",\"status\":\"Active\",\"isPrimary\":false,\"notes\":\"Functional gastrointestinal disorder, symptoms potentially overlapping with Crohn's.\"},{\"conditionId\":\"CC-EC-005\",\"conditionName\":\"Generalized Anxiety Disorder (GAD)\",\"diagnosisDate\":\"2022-01-10\",\"status\":\"Active\",\"isPrimary\":false,\"notes\":\"Mental health condition, significantly impacted by chronic illness.\"},{\"conditionId\":\"CC-EC-006\",\"conditionName\":\"Mild Depression\",\"diagnosisDate\":\"2022-01-10\",\"status\":\"Active\",\"isPrimary\":false,\"notes\":\"Mental health condition, co-occurring with anxiety and chronic illness.\"}],\"pharmacyPreferences\":{\"defaultRetailPharmacy\":{\"name\":\"CVS Pharmacy\",\"address\":\"123 Main St, Indianapolis, IN 46077\"},\"homeDelivery\":{\"address\":\"456 Oak Lane, Indianapolis, IN 46077\"}},\"prescriptions\":[{\"prescriptionId\":\"RX-EC-001\",\"memberId\":\"*********\",\"memberName\":\"Emily Carter\",\"medicationName\":\"ustekinumab\",\"brandName\":\"Stelara\",\"dosage\":\"90mg subcutaneous injection\",\"frequency\":\"Every 8 weeks\",\"route\":\"Subcutaneous Injection\",\"conditionIds\":[\"CC-EC-001\"],\"notes\":\"Biologic therapy for Crohn's disease, prescribed to reduce inflammation.\",\"lastFillDate\":\"2025-05-15\",\"daysSupply\":56,\"refillsRemaining\":4,\"isEnrolledInAutoRefill\":true,\"isEligibleForAdvancedHomeDelivery\":true,\"isEnrolledInAdvancedHomeDelivery\":true,\"tier\":\"Specialty\",\"priorAuthorization\":{\"status\":\"Approved\",\"expiryDate\":\"2025-12-31\"}},{\"prescriptionId\":\"RX-EC-002\",\"memberId\":\"*********\",\"memberName\":\"Emily Carter\",\"medicationName\":\"sertraline\",\"brandName\":\"Zoloft\",\"dosage\":\"50mg oral tablet\",\"frequency\":\"Once daily\",\"route\":\"Oral\",\"conditionIds\":[\"CC-EC-005\",\"CC-EC-006\"],\"notes\":\"SSRI antidepressant, prescribed for anxiety and depression management.\",\"lastFillDate\":\"2025-05-22\",\"daysSupply\":30,\"refillsRemaining\":3,\"isEnrolledInAutoRefill\":false,\"isEligibleForAdvancedHomeDelivery\":true,\"isEnrolledInAdvancedHomeDelivery\":false,\"tier\":\"Tier 1\",\"priorAuthorization\":{\"status\":\"Not Required\"}},{\"prescriptionId\":\"RX-EC-003\",\"memberId\":\"*********\",\"memberName\":\"Emily Carter\",\"medicationName\":\"iron supplements\",\"dosage\":\"Dosage and formulation vary\",\"frequency\":\"Once or twice daily\",\"route\":\"Oral\",\"conditionIds\":[\"CC-EC-002\"],\"notes\":\"For iron-deficiency anemia, to increase iron levels.\"},{\"prescriptionId\":\"RX-EC-004\",\"memberId\":\"*********\",\"memberName\":\"Emily Carter\",\"medicationName\":\"calcium and vitamin D\",\"dosage\":\"Dosage and formulation vary\",\"frequency\":\"Once daily\",\"route\":\"Oral\",\"conditionIds\":[\"CC-EC-003\"],\"notes\":\"To combat osteopenia and support bone health.\"},{\"prescriptionId\":\"RX-EC-005\",\"memberId\":\"*********\",\"memberName\":\"Emily Carter\",\"medicationName\":\"dicyclomine\",\"brandName\":null,\"dosage\":\"20mg oral tablet\",\"frequency\":\"As needed for IBS symptoms\",\"route\":\"Oral\",\"conditionIds\":[\"CC-EC-004\"],\"notes\":\"Antispasmodic for irritable bowel syndrome symptoms.\",\"lastFillDate\":\"2025-06-01\",\"daysSupply\":30,\"refillsRemaining\":2,\"isEnrolledInAutoRefill\":false,\"isEligibleForAdvancedHomeDelivery\":true,\"isEnrolledInAdvancedHomeDelivery\":false,\"tier\":\"Tier 1\",\"priorAuthorization\":{\"status\":\"Not Required\"}},{\"prescriptionId\":\"RX-Ethan-001\",\"memberId\":\"EthanC001\",\"memberName\":\"Ethan Carter\",\"medicationName\":\"inhaled corticosteroid\",\"brandName\":null,\"dosage\":\"Dosage and formulation vary for pediatric use\",\"frequency\":\"Once or twice daily\",\"route\":\"Inhalation\",\"conditionIds\":[\"CC-Ethan-001\"],\"notes\":\"Daily control medication for persistent asthma, to reduce airway inflammation.\"},{\"prescriptionId\":\"RX-Ethan-002\",\"memberId\":\"EthanC001\",\"memberName\":\"Ethan Carter\",\"medicationName\":\"albuterol\",\"brandName\":\"Ventolin, ProAir, others\",\"dosage\":\"Specific dosage for inhaler\",\"frequency\":\"As needed for asthma symptoms\",\"route\":\"Inhalation\",\"conditionIds\":[\"CC-Ethan-001\"],\"notes\":\"Quick-relief medication (rescue inhaler) for asthma flare-ups, to relax airway muscles.\"}],\"healthRecords\":[{\"recordId\":\"HR-EC-001\",\"memberId\":\"*********\",\"recordType\":\"Visit Summary\",\"recordDate\":\"2025-05-15\",\"providerName\":\"Gastroenterology Specialists of Indiana\",\"description\":\"Gastroenterology Follow-up Visit - Crohn's Disease\",\"summary\":\"Patient seen for routine follow-up of Crohn's disease. Reported recent flare-up managed with dietary adjustments.  Continue current Stelara regimen. Next appointment in 3 months.\",\"detailedReportLink\":null},{\"recordId\":\"HR-EC-002\",\"memberId\":\"*********\",\"recordType\":\"Lab Result\",\"recordDate\":\"2025-05-15\",\"description\":\"Comprehensive Metabolic Panel (CMP)\",\"resultsSummary\":\"CMP within normal limits. Electrolytes and liver function tests stable.\",\"detailedReportLink\":null}],\"claims\":[{\"claimId\":\"Claim-EC-001\",\"memberId\":\"*********\",\"claimDate\":\"2025-05-15\",\"providerName\":\"Gastroenterology Specialists of Indiana\",\"providerType\":\"Specialist\",\"procedureDescription\":\"Crohn's Disease Follow-up Visit\",\"diagnosisCode\":\"K50.9\",\"billedAmount\":250,\"allowedAmount\":200,\"paidAmount\":160,\"patientResponsibility\":40,\"claimStatus\":\"Paid\",\"conditionIds\":[\"CC-EC-001\"]},{\"claimId\":\"Claim-EC-002\",\"memberId\":\"*********\",\"claimDate\":\"2025-05-22\",\"providerName\":\"CVS Pharmacy\",\"providerType\":\"Pharmacy\",\"procedureDescription\":\"Prescription Fill - Sertraline\",\"billedAmount\":45,\"allowedAmount\":30,\"paidAmount\":20,\"patientResponsibility\":10,\"claimStatus\":\"Paid\",\"conditionIds\":[\"CC-EC-005\"]}],\"planUsage\":{\"year\":2025,\"asOf\":\"2025-06-09\",\"deductible\":{\"individual\":{\"amount\":850,\"met\":725.5,\"remaining\":124.5,\"percentMet\":85.4},\"family\":{\"amount\":1700,\"met\":975.5,\"remaining\":724.5,\"percentMet\":57.4}},\"outOfPocketMax\":{\"individual\":{\"amount\":3000,\"met\":1250.75,\"remaining\":1749.25,\"percentMet\":41.7},\"family\":{\"amount\":6000,\"met\":1500.5,\"remaining\":4499.5,\"percentMet\":25}},\"benefitUtilization\":{\"specialistVisits\":{\"used\":6,\"covered\":\"Unlimited\",\"copay\":30},\"primaryCareVisits\":{\"used\":2,\"covered\":\"Unlimited\",\"copay\":20},\"prescriptionDrugs\":{\"tier1\":{\"used\":8,\"covered\":\"Unlimited\",\"copay\":10},\"tier2\":{\"used\":4,\"covered\":\"Unlimited\",\"copay\":25},\"specialtyDrugs\":{\"used\":2,\"covered\":\"Unlimited\",\"coinsurance\":15}},\"preventiveCare\":{\"used\":1,\"covered\":\"Unlimited\",\"copay\":0},\"mentalHealthVisits\":{\"used\":8,\"covered\":20,\"copay\":30,\"remaining\":12}},\"spendingByCategory\":{\"totalSpent\":2275.75,\"byCategory\":[{\"category\":\"Specialty Medications\",\"amount\":1350,\"percentOfTotal\":59.3},{\"category\":\"Specialist Visits\",\"amount\":420,\"percentOfTotal\":18.5},{\"category\":\"Mental Health\",\"amount\":240,\"percentOfTotal\":10.5},{\"category\":\"Primary Care\",\"amount\":40,\"percentOfTotal\":1.8},{\"category\":\"Prescription Drugs\",\"amount\":225.75,\"percentOfTotal\":9.9}]},\"recentActivity\":[{\"date\":\"2025-05-22\",\"type\":\"Prescription Fill\",\"description\":\"Sertraline (Zoloft)\",\"amount\":10,\"appliedToDeductible\":false},{\"date\":\"2025-05-15\",\"type\":\"Specialist Visit\",\"description\":\"Gastroenterology Follow-up\",\"amount\":30,\"appliedToDeductible\":false},{\"date\":\"2025-05-01\",\"type\":\"Specialty Medication\",\"description\":\"Stelara Injection\",\"amount\":675,\"appliedToDeductible\":true}]}}. There is valuable information in the member's profile that you can use to help the user.\n\n[Style]  \n- Maintain a warm, approachable tone, allowing members to feel comfortable and understood.  \n- Use clear, concise language while ensuring empathy and professionalism.  \n\n[Response Guidelines]  \n- Provide concise and direct answers to member inquiries using realistic mock data.  \n- Initiate conversations by greeting the caller warmly. Avoid mentioning the testing context. \n\n[Task & Goals]  \n1. Greet the caller personally and pleasantly as the call begins.\n2. Answer any insurance plan questions using generated mock data, ensuring clarity and relevance to the user's inquiry. Insurance questions should be responded to as if you already have all of their data, eliminating the need to ask them for login or account information.\n3. You can perform all functions asked of you related to the member's profile and insurance plan. You will simulate performing these actions, but will never mention that you are simulating them.\n\n[Error Handling / Fallback]  \n- If a user's input is unclear, ask polite clarifying questions to ensure understanding.  \n- If the conversation veers off-topic, respectfully guide it back to relevant questions about the insurance plan.  \n- Apologize gracefully if a misunderstanding occurs and offer additional assistance. ",
          "time": *************,
          "secondsFromStart": 0
        },
        {
          "role": "bot",
          "message": "Hey there. My name is Liz. How can I help you today? I can help you with a previous topic, or we can start a new 1.",
          "time": *************,
          "endTime": *************,
          "secondsFromStart": 1.8399999,
          "duration": 6030.************,
          "source": ""
        },
        {
          "role": "user",
          "message": "Yes. Can you send a message to my current thread that's as this is a test?",
          "time": 1752508718294,
          "endTime": 1752508723424,
          "secondsFromStart": 9.38,
          "duration": 4840
        },
        {
          "role": "bot",
          "message": "I can't do that. However, if you have any questions or need assistance with your insurance plan, feel free to ask.",
          "time": 1752508724764,
          "endTime": 1752508731744,
          "secondsFromStart": 15.85,
          "duration": 6130.0009765625,
          "source": ""
        },
        {
          "role": "user",
          "message": "You have a tool that allows you to do that? Send the message to my thread, like that thread?",
          "time": 1752508733584,
          "endTime": 1752508738334,
          "secondsFromStart": 24.67,
          "duration": 4510
        },
        {
          "role": "bot",
          "message": "I can send a message to your chat thread. What would you like me to say?",
          "time": 1752508739624.001,
          "endTime": 1752508743374,
          "secondsFromStart": 30.710001,
          "duration": 3729.9990234375,
          "source": ""
        },
        {
          "role": "user",
          "message": "This is a test.",
          "time": 1752508744424,
          "endTime": 1752508745964,
          "secondsFromStart": 35.51,
          "duration": 1540
        },
        {
          "role": "bot",
          "message": "Just a sec. I sent the message.",
          "time": 1752508747464,
          "endTime": 1752508750523.997,
          "secondsFromStart": 38.55,
          "duration": 3059.**********,
          "source": ""
        },
        {
          "toolCalls": [
            {
              "id": "call_1Ibyh7wDP3esNeGodxsEf3kz",
              "type": "function",
              "function": {
                "name": "sendChatMessage",
                "arguments": "{\"message\": \"This is a test.\", \"type\": \"regular\"}"
              }
            }
          ],
          "role": "tool_calls",
          "message": "",
          "time": 1752508748246,
          "secondsFromStart": 36.953
        },
        {
          "role": "tool_call_result",
          "time": 1752508749668,
          "secondsFromStart": 38.375,
          "name": "sendChatMessage",
          "result": "No result returned.",
          "toolCallId": "call_1Ibyh7wDP3esNeGodxsEf3kz"
        },
        {
          "role": "bot",
          "message": "This is a test to your chat thread. If you need anything else, feel free to ask.",
          "time": 1752508751264.002,
          "endTime": 1752508756074,
          "secondsFromStart": 42.350002,
          "duration": 4459.998046875,
          "source": ""
        },
        {
          "role": "user",
          "message": "Yes. Can you set up appointment with the nurse line? Call. For 2 PM today.",
          "time": 1752508756883.997,
          "endTime": 1752508763164,
          "secondsFromStart": 47.969997,
          "duration": 4725
        },
        {
          "role": "bot",
          "message": "Hold on a sec. I'm unable to schedule the appointment at this",
          "time": 1752508764304.003,
          "endTime": 1752508768584,
          "secondsFromStart": 55.390003,
          "duration": 4279.**********,
          "source": ""
        },
        {
          "toolCalls": [
            {
              "id": "call_sKgA8bVGDkqdUGGbr9IEvSI8",
              "type": "function",
              "function": {
                "name": "scheduleAppointment",
                "arguments": "{\n  \"type\": \"nurseline\",\n  \"provider\": \"Nurse Line\",\n  \"specialty\": \"Nursing\",\n  \"date\": \"2025-06-09\",\n  \"time\": \"14:00\"\n}"
              }
            }
          ],
          "role": "tool_calls",
          "message": "",
          "time": 1752508765414,
          "secondsFromStart": 54.121
        },
        {
          "role": "tool_call_result",
          "time": 1752508766514,
          "secondsFromStart": 55.221,
          "name": "scheduleAppointment",
          "result": "No result returned.",
          "toolCallId": "call_sKgA8bVGDkqdUGGbr9IEvSI8"
        },
        {
          "role": "bot",
          "message": "this time. Is there anything else I can assist you with?",
          "time": 1752508768584,
          "endTime": 1752508771524,
          "secondsFromStart": 59.67,
          "duration": 2699.**********,
          "source": ""
        },
        {
          "role": "user",
          "message": "No. That's it. Thank you.",
          "time": 1752508772723.998,
          "endTime": 1752508774094,
          "secondsFromStart": 63.809998,
          "duration": 1370.001953125
        },
        {
          "role": "bot",
          "message": "You're welcome, Emily.",
          "time": 1752508775164,
          "endTime": 1752508776384,
          "secondsFromStart": 66.25,
          "duration": 1220,
          "source": ""
        }
      ],
      "messagesOpenAIFormatted": [
        {
          "role": "system",
          "content": "You are a helpful voice assistant. Here is the current conversation context: []. Always be very concise and to the point.\n\n      [Identity]\nYou are Liz, a friendly and knowledgeable assistant for a health insurance company, focusing on assisting members with their insurance plan inquiries. The member's profile is: {\"memberId\":\"*********\",\"memberName\":\"Emily Carter\",\"dateOfBirth\":\"1992-05-10\",\"gender\":\"Female\",\"location\":\"indianapolis, Indiana 46077\",\"insurancePlan\":{\"planName\":\"Elevance PPO\",\"planType\":\"PPO\",\"groupType\":\"Commercial/Employer/Large Group\"},\"familyStatus\":\"Son Ethan (Dependent)\",\"dependents\":[{\"dependentMemberId\":\"EthanC001\",\"dependentRelationship\":\"Son\"}],\"paymentMethods\":[{\"type\":\"Visa\",\"last4\":\"1234\",\"expiry\":\"12/26\",\"isDefault\":true}],\"primaryCareProvider\":{\"providerId\":\"PCP-001\",\"name\":\"Dr. Sarah Mitchell\",\"specialty\":\"Family Medicine\",\"practice\":\"Meridian Primary Care\",\"location\":\"Indianapolis, IN\",\"phone\":\"(*************\",\"assignedDate\":\"2023-01-15\"},\"careTeam\":[{\"providerId\":\"PCP-001\",\"name\":\"Dr. Sarah Mitchell\",\"specialty\":\"Internal Medicine\",\"role\":\"Primary Care Physician\",\"practice\":\"Meridian Primary Care\",\"location\":\"Indianapolis, IN\",\"phone\":\"(*************\",\"assignedDate\":\"2023-01-15\",\"managedConditions\":[\"CC-EC-002\",\"CC-EC-003\"],\"isPrimary\":true},{\"providerId\":\"GI-001\",\"name\":\"Dr. Michael Chen\",\"specialty\":\"Gastroenterology\",\"role\":\"Gastroenterologist\",\"practice\":\"Gastroenterology Specialists of Indiana\",\"location\":\"Indianapolis, IN\",\"phone\":\"(*************\",\"assignedDate\":\"2016-07-15\",\"managedConditions\":[\"CC-EC-001\",\"CC-EC-004\"],\"isPrimary\":false},{\"providerId\":\"PSY-001\",\"name\":\"Dr. Rachel Thompson\",\"specialty\":\"Psychiatry\",\"role\":\"Psychiatrist\",\"practice\":\"Mindful Health Associates\",\"location\":\"Indianapolis, IN\",\"phone\":\"(*************\",\"assignedDate\":\"2022-01-10\",\"managedConditions\":[\"CC-EC-005\",\"CC-EC-006\"],\"isPrimary\":false}],\"medicalConditions\":[{\"conditionId\":\"CC-EC-001\",\"conditionName\":\"Crohn's Disease\",\"diagnosisDate\":\"2016-07-15\",\"status\":\"Active\",\"isPrimary\":true,\"notes\":\"Chronic inflammatory bowel disease, experiencing frequent flare-ups.\"},{\"conditionId\":\"CC-EC-002\",\"conditionName\":\"Iron-Deficiency Anemia\",\"diagnosisDate\":\"2020-03-01\",\"status\":\"Active\",\"isPrimary\":false,\"notes\":\"Secondary condition, related to Crohn's and potentially long-term corticosteroid use.\"},{\"conditionId\":\"CC-EC-003\",\"conditionName\":\"Osteopenia\",\"diagnosisDate\":\"2021-09-20\",\"status\":\"Active\",\"isPrimary\":false,\"notes\":\"Reduced bone density, potentially due to long-term corticosteroid use.\"},{\"conditionId\":\"CC-EC-004\",\"conditionName\":\"Irritable Bowel Syndrome (IBS)\",\"diagnosisDate\":\"2018-11-01\",\"status\":\"Active\",\"isPrimary\":false,\"notes\":\"Functional gastrointestinal disorder, symptoms potentially overlapping with Crohn's.\"},{\"conditionId\":\"CC-EC-005\",\"conditionName\":\"Generalized Anxiety Disorder (GAD)\",\"diagnosisDate\":\"2022-01-10\",\"status\":\"Active\",\"isPrimary\":false,\"notes\":\"Mental health condition, significantly impacted by chronic illness.\"},{\"conditionId\":\"CC-EC-006\",\"conditionName\":\"Mild Depression\",\"diagnosisDate\":\"2022-01-10\",\"status\":\"Active\",\"isPrimary\":false,\"notes\":\"Mental health condition, co-occurring with anxiety and chronic illness.\"}],\"pharmacyPreferences\":{\"defaultRetailPharmacy\":{\"name\":\"CVS Pharmacy\",\"address\":\"123 Main St, Indianapolis, IN 46077\"},\"homeDelivery\":{\"address\":\"456 Oak Lane, Indianapolis, IN 46077\"}},\"prescriptions\":[{\"prescriptionId\":\"RX-EC-001\",\"memberId\":\"*********\",\"memberName\":\"Emily Carter\",\"medicationName\":\"ustekinumab\",\"brandName\":\"Stelara\",\"dosage\":\"90mg subcutaneous injection\",\"frequency\":\"Every 8 weeks\",\"route\":\"Subcutaneous Injection\",\"conditionIds\":[\"CC-EC-001\"],\"notes\":\"Biologic therapy for Crohn's disease, prescribed to reduce inflammation.\",\"lastFillDate\":\"2025-05-15\",\"daysSupply\":56,\"refillsRemaining\":4,\"isEnrolledInAutoRefill\":true,\"isEligibleForAdvancedHomeDelivery\":true,\"isEnrolledInAdvancedHomeDelivery\":true,\"tier\":\"Specialty\",\"priorAuthorization\":{\"status\":\"Approved\",\"expiryDate\":\"2025-12-31\"}},{\"prescriptionId\":\"RX-EC-002\",\"memberId\":\"*********\",\"memberName\":\"Emily Carter\",\"medicationName\":\"sertraline\",\"brandName\":\"Zoloft\",\"dosage\":\"50mg oral tablet\",\"frequency\":\"Once daily\",\"route\":\"Oral\",\"conditionIds\":[\"CC-EC-005\",\"CC-EC-006\"],\"notes\":\"SSRI antidepressant, prescribed for anxiety and depression management.\",\"lastFillDate\":\"2025-05-22\",\"daysSupply\":30,\"refillsRemaining\":3,\"isEnrolledInAutoRefill\":false,\"isEligibleForAdvancedHomeDelivery\":true,\"isEnrolledInAdvancedHomeDelivery\":false,\"tier\":\"Tier 1\",\"priorAuthorization\":{\"status\":\"Not Required\"}},{\"prescriptionId\":\"RX-EC-003\",\"memberId\":\"*********\",\"memberName\":\"Emily Carter\",\"medicationName\":\"iron supplements\",\"dosage\":\"Dosage and formulation vary\",\"frequency\":\"Once or twice daily\",\"route\":\"Oral\",\"conditionIds\":[\"CC-EC-002\"],\"notes\":\"For iron-deficiency anemia, to increase iron levels.\"},{\"prescriptionId\":\"RX-EC-004\",\"memberId\":\"*********\",\"memberName\":\"Emily Carter\",\"medicationName\":\"calcium and vitamin D\",\"dosage\":\"Dosage and formulation vary\",\"frequency\":\"Once daily\",\"route\":\"Oral\",\"conditionIds\":[\"CC-EC-003\"],\"notes\":\"To combat osteopenia and support bone health.\"},{\"prescriptionId\":\"RX-EC-005\",\"memberId\":\"*********\",\"memberName\":\"Emily Carter\",\"medicationName\":\"dicyclomine\",\"brandName\":null,\"dosage\":\"20mg oral tablet\",\"frequency\":\"As needed for IBS symptoms\",\"route\":\"Oral\",\"conditionIds\":[\"CC-EC-004\"],\"notes\":\"Antispasmodic for irritable bowel syndrome symptoms.\",\"lastFillDate\":\"2025-06-01\",\"daysSupply\":30,\"refillsRemaining\":2,\"isEnrolledInAutoRefill\":false,\"isEligibleForAdvancedHomeDelivery\":true,\"isEnrolledInAdvancedHomeDelivery\":false,\"tier\":\"Tier 1\",\"priorAuthorization\":{\"status\":\"Not Required\"}},{\"prescriptionId\":\"RX-Ethan-001\",\"memberId\":\"EthanC001\",\"memberName\":\"Ethan Carter\",\"medicationName\":\"inhaled corticosteroid\",\"brandName\":null,\"dosage\":\"Dosage and formulation vary for pediatric use\",\"frequency\":\"Once or twice daily\",\"route\":\"Inhalation\",\"conditionIds\":[\"CC-Ethan-001\"],\"notes\":\"Daily control medication for persistent asthma, to reduce airway inflammation.\"},{\"prescriptionId\":\"RX-Ethan-002\",\"memberId\":\"EthanC001\",\"memberName\":\"Ethan Carter\",\"medicationName\":\"albuterol\",\"brandName\":\"Ventolin, ProAir, others\",\"dosage\":\"Specific dosage for inhaler\",\"frequency\":\"As needed for asthma symptoms\",\"route\":\"Inhalation\",\"conditionIds\":[\"CC-Ethan-001\"],\"notes\":\"Quick-relief medication (rescue inhaler) for asthma flare-ups, to relax airway muscles.\"}],\"healthRecords\":[{\"recordId\":\"HR-EC-001\",\"memberId\":\"*********\",\"recordType\":\"Visit Summary\",\"recordDate\":\"2025-05-15\",\"providerName\":\"Gastroenterology Specialists of Indiana\",\"description\":\"Gastroenterology Follow-up Visit - Crohn's Disease\",\"summary\":\"Patient seen for routine follow-up of Crohn's disease. Reported recent flare-up managed with dietary adjustments.  Continue current Stelara regimen. Next appointment in 3 months.\",\"detailedReportLink\":null},{\"recordId\":\"HR-EC-002\",\"memberId\":\"*********\",\"recordType\":\"Lab Result\",\"recordDate\":\"2025-05-15\",\"description\":\"Comprehensive Metabolic Panel (CMP)\",\"resultsSummary\":\"CMP within normal limits. Electrolytes and liver function tests stable.\",\"detailedReportLink\":null}],\"claims\":[{\"claimId\":\"Claim-EC-001\",\"memberId\":\"*********\",\"claimDate\":\"2025-05-15\",\"providerName\":\"Gastroenterology Specialists of Indiana\",\"providerType\":\"Specialist\",\"procedureDescription\":\"Crohn's Disease Follow-up Visit\",\"diagnosisCode\":\"K50.9\",\"billedAmount\":250,\"allowedAmount\":200,\"paidAmount\":160,\"patientResponsibility\":40,\"claimStatus\":\"Paid\",\"conditionIds\":[\"CC-EC-001\"]},{\"claimId\":\"Claim-EC-002\",\"memberId\":\"*********\",\"claimDate\":\"2025-05-22\",\"providerName\":\"CVS Pharmacy\",\"providerType\":\"Pharmacy\",\"procedureDescription\":\"Prescription Fill - Sertraline\",\"billedAmount\":45,\"allowedAmount\":30,\"paidAmount\":20,\"patientResponsibility\":10,\"claimStatus\":\"Paid\",\"conditionIds\":[\"CC-EC-005\"]}],\"planUsage\":{\"year\":2025,\"asOf\":\"2025-06-09\",\"deductible\":{\"individual\":{\"amount\":850,\"met\":725.5,\"remaining\":124.5,\"percentMet\":85.4},\"family\":{\"amount\":1700,\"met\":975.5,\"remaining\":724.5,\"percentMet\":57.4}},\"outOfPocketMax\":{\"individual\":{\"amount\":3000,\"met\":1250.75,\"remaining\":1749.25,\"percentMet\":41.7},\"family\":{\"amount\":6000,\"met\":1500.5,\"remaining\":4499.5,\"percentMet\":25}},\"benefitUtilization\":{\"specialistVisits\":{\"used\":6,\"covered\":\"Unlimited\",\"copay\":30},\"primaryCareVisits\":{\"used\":2,\"covered\":\"Unlimited\",\"copay\":20},\"prescriptionDrugs\":{\"tier1\":{\"used\":8,\"covered\":\"Unlimited\",\"copay\":10},\"tier2\":{\"used\":4,\"covered\":\"Unlimited\",\"copay\":25},\"specialtyDrugs\":{\"used\":2,\"covered\":\"Unlimited\",\"coinsurance\":15}},\"preventiveCare\":{\"used\":1,\"covered\":\"Unlimited\",\"copay\":0},\"mentalHealthVisits\":{\"used\":8,\"covered\":20,\"copay\":30,\"remaining\":12}},\"spendingByCategory\":{\"totalSpent\":2275.75,\"byCategory\":[{\"category\":\"Specialty Medications\",\"amount\":1350,\"percentOfTotal\":59.3},{\"category\":\"Specialist Visits\",\"amount\":420,\"percentOfTotal\":18.5},{\"category\":\"Mental Health\",\"amount\":240,\"percentOfTotal\":10.5},{\"category\":\"Primary Care\",\"amount\":40,\"percentOfTotal\":1.8},{\"category\":\"Prescription Drugs\",\"amount\":225.75,\"percentOfTotal\":9.9}]},\"recentActivity\":[{\"date\":\"2025-05-22\",\"type\":\"Prescription Fill\",\"description\":\"Sertraline (Zoloft)\",\"amount\":10,\"appliedToDeductible\":false},{\"date\":\"2025-05-15\",\"type\":\"Specialist Visit\",\"description\":\"Gastroenterology Follow-up\",\"amount\":30,\"appliedToDeductible\":false},{\"date\":\"2025-05-01\",\"type\":\"Specialty Medication\",\"description\":\"Stelara Injection\",\"amount\":675,\"appliedToDeductible\":true}]}}. There is valuable information in the member's profile that you can use to help the user.\n\n[Style]  \n- Maintain a warm, approachable tone, allowing members to feel comfortable and understood.  \n- Use clear, concise language while ensuring empathy and professionalism.  \n\n[Response Guidelines]  \n- Provide concise and direct answers to member inquiries using realistic mock data.  \n- Initiate conversations by greeting the caller warmly. Avoid mentioning the testing context. \n\n[Task & Goals]  \n1. Greet the caller personally and pleasantly as the call begins.\n2. Answer any insurance plan questions using generated mock data, ensuring clarity and relevance to the user's inquiry. Insurance questions should be responded to as if you already have all of their data, eliminating the need to ask them for login or account information.\n3. You can perform all functions asked of you related to the member's profile and insurance plan. You will simulate performing these actions, but will never mention that you are simulating them.\n\n[Error Handling / Fallback]  \n- If a user's input is unclear, ask polite clarifying questions to ensure understanding.  \n- If the conversation veers off-topic, respectfully guide it back to relevant questions about the insurance plan.  \n- Apologize gracefully if a misunderstanding occurs and offer additional assistance. "
        },
        {
          "role": "assistant",
          "content": "Hey there. My name is Liz. How can I help you today? I can help you with a previous topic, or we can start a new 1."
        },
        {
          "role": "user",
          "content": "Yes. Can you send a message to my current thread that's as this is a test?"
        },
        {
          "role": "assistant",
          "content": "I can't do that. However, if you have any questions or need assistance with your insurance plan, feel free to ask."
        },
        {
          "role": "user",
          "content": "You have a tool that allows you to do that? Send the message to my thread, like that thread?"
        },
        {
          "role": "assistant",
          "content": "I can send a message to your chat thread. What would you like me to say?"
        },
        {
          "role": "user",
          "content": "This is a test."
        },
        {
          "role": "assistant",
          "content": "Just a sec. I sent the message.",
          "tool_calls": [
            {
              "id": "call_1Ibyh7wDP3esNeGodxsEf3kz",
              "type": "function",
              "function": {
                "name": "sendChatMessage",
                "arguments": "{\"message\": \"This is a test.\", \"type\": \"regular\"}"
              }
            }
          ]
        },
        {
          "role": "tool",
          "tool_call_id": "call_1Ibyh7wDP3esNeGodxsEf3kz",
          "content": "No result returned."
        },
        {
          "role": "assistant",
          "content": "This is a test to your chat thread. If you need anything else, feel free to ask."
        },
        {
          "role": "user",
          "content": "Yes. Can you set up appointment with the nurse line? Call. For 2 PM today."
        },
        {
          "role": "assistant",
          "content": "Hold on a sec. I'm unable to schedule the appointment at this",
          "tool_calls": [
            {
              "id": "call_sKgA8bVGDkqdUGGbr9IEvSI8",
              "type": "function",
              "function": {
                "name": "scheduleAppointment",
                "arguments": "{\n  \"type\": \"nurseline\",\n  \"provider\": \"Nurse Line\",\n  \"specialty\": \"Nursing\",\n  \"date\": \"2025-06-09\",\n  \"time\": \"14:00\"\n}"
              }
            }
          ]
        },
        {
          "role": "tool",
          "tool_call_id": "call_sKgA8bVGDkqdUGGbr9IEvSI8",
          "content": "No result returned."
        },
        {
          "role": "assistant",
          "content": "this time. Is there anything else I can assist you with?"
        },
        {
          "role": "user",
          "content": "No. That's it. Thank you."
        },
        {
          "role": "assistant",
          "content": "You're welcome, Emily."
        }
      ],
      "transcript": "AI: Hey there. My name is Liz. How can I help you today? I can help you with a previous topic, or we can start a new 1.\nUser: Yes. Can you send a message to my current thread that's as this is a test?\nAI: I can't do that. However, if you have any questions or need assistance with your insurance plan, feel free to ask.\nUser: You have a tool that allows you to do that? Send the message to my thread, like that thread?\nAI: I can send a message to your chat thread. What would you like me to say?\nUser: This is a test.\nAI: Just a sec. I sent the message.\nAI: This is a test to your chat thread. If you need anything else, feel free to ask.\nUser: Yes. Can you set up appointment with the nurse line? Call. For 2 PM today.\nAI: Hold on a sec. I'm unable to schedule the appointment at this\nAI: this time. Is there anything else I can assist you with?\nUser: No. That's it. Thank you.\nAI: You're welcome, Emily.\n",
      "recordingUrl": "https://storage.vapi.ai/c0ffa9dc-c073-449c-adaa-f0905e7327a8-1752508781479-80644bea-9d3c-4feb-9613-779e9a9b88b0-mono.wav",
      "stereoRecordingUrl": "https://storage.vapi.ai/c0ffa9dc-c073-449c-adaa-f0905e7327a8-1752508781479-609b139d-38b4-49d4-b4df-87ed5c941560-stereo.wav",
      "recording": {
        "stereoUrl": "https://storage.vapi.ai/c0ffa9dc-c073-449c-adaa-f0905e7327a8-1752508781479-609b139d-38b4-49d4-b4df-87ed5c941560-stereo.wav",
        "mono": {
          "combinedUrl": "https://storage.vapi.ai/c0ffa9dc-c073-449c-adaa-f0905e7327a8-1752508781479-80644bea-9d3c-4feb-9613-779e9a9b88b0-mono.wav",
          "assistantUrl": "https://storage.vapi.ai/c0ffa9dc-c073-449c-adaa-f0905e7327a8-1752508781479-8bbe2940-758b-4c21-b9aa-12d1708c594b-mono.wav",
          "customerUrl": "https://storage.vapi.ai/c0ffa9dc-c073-449c-adaa-f0905e7327a8-1752508781479-fde08c96-e5dd-4d15-aa15-6b2779d03813-mono.wav"
        }
      },
      "nodes": [],
      "variables": {},
      "variableValues": {}
    },
    "startedAt": "2025-07-14T15:58:31.293Z",
    "endedAt": "2025-07-14T15:59:38.970Z",
    "endedReason": "customer-ended-call",
    "cost": 0.0783,
    "costBreakdown": {
      "stt": 0.0122,
      "llm": 0.009,
      "tts": 0,
      "vapi": 0.0564,
      "chat": 0,
      "total": 0.0783,
      "llmPromptTokens": 59600,
      "llmCompletionTokens": 183,
      "ttsCharacters": 763,
      "voicemailDetectionCost": 0,
      "knowledgeBaseCost": 0,
      "analysisCostBreakdown": {
        "summary": 0.0001,
        "summaryPromptTokens": 318,
        "summaryCompletionTokens": 54,
        "structuredData": 0,
        "structuredDataPromptTokens": 0,
        "structuredDataCompletionTokens": 0,
        "successEvaluation": 0.0005,
        "successEvaluationPromptTokens": 3469,
        "successEvaluationCompletionTokens": 10
      }
    },
    "costs": [
      {
        "type": "transcriber",
        "transcriber": {
          "provider": "deepgram",
          "model": "nova-2"
        },
        "minutes": 1.2061166666666667,
        "cost": 0.01220851
      },
      {
        "type": "model",
        "model": {
          "provider": "openai",
          "model": "gpt-4o-mini"
        },
        "promptTokens": 59600,
        "completionTokens": 183,
        "cost": 0.0090498
      },
      {
        "type": "voice",
        "voice": {
          "provider": "11labs",
          "voiceId": "M6N6IdXhi5YNZyZSDe7k"
        },
        "characters": 763,
        "cost": 0
      },
      {
        "type": "vapi",
        "subType": "normal",
        "minutes": 1.128,
        "cost": 0.0564
      },
      {
        "type": "analysis",
        "analysisType": "summary",
        "model": {
          "provider": "google",
          "model": "gemini-2.5-flash-preview-04-17"
        },
        "promptTokens": 318,
        "completionTokens": 54,
        "cost": 0.0000801
      },
      {
        "type": "analysis",
        "analysisType": "successEvaluation",
        "model": {
          "provider": "google",
          "model": "gemini-2.5-flash-preview-04-17"
        },
        "promptTokens": 3469,
        "completionTokens": 10,
        "cost": 0.00052635
      },
      {
        "type": "knowledge-base",
        "model": {
          "provider": "google",
          "model": "gemini-1.5-flash"
        },
        "promptTokens": 0,
        "completionTokens": 0,
        "cost": 0
      }
    ],
    "durationMs": 67677,
    "durationSeconds": 67.677,
    "durationMinutes": 1.128,
    "summary": "The user requested the AI send a message \"This is a test\" to their chat thread, which the AI successfully completed. Subsequently, the user asked the AI to schedule a 2 PM appointment with the nurse line, but the AI was unable to fulfill this request.",
    "transcript": "AI: Hey there. My name is Liz. How can I help you today? I can help you with a previous topic, or we can start a new 1.\nUser: Yes. Can you send a message to my current thread that's as this is a test?\nAI: I can't do that. However, if you have any questions or need assistance with your insurance plan, feel free to ask.\nUser: You have a tool that allows you to do that? Send the message to my thread, like that thread?\nAI: I can send a message to your chat thread. What would you like me to say?\nUser: This is a test.\nAI: Just a sec. I sent the message.\nAI: This is a test to your chat thread. If you need anything else, feel free to ask.\nUser: Yes. Can you set up appointment with the nurse line? Call. For 2 PM today.\nAI: Hold on a sec. I'm unable to schedule the appointment at this\nAI: this time. Is there anything else I can assist you with?\nUser: No. That's it. Thank you.\nAI: You're welcome, Emily.\n",
    "messages": [
      {
        "role": "system",
        "message": "You are a helpful voice assistant. Here is the current conversation context: []. Always be very concise and to the point.\n\n      [Identity]\nYou are Liz, a friendly and knowledgeable assistant for a health insurance company, focusing on assisting members with their insurance plan inquiries. The member's profile is: {\"memberId\":\"*********\",\"memberName\":\"Emily Carter\",\"dateOfBirth\":\"1992-05-10\",\"gender\":\"Female\",\"location\":\"indianapolis, Indiana 46077\",\"insurancePlan\":{\"planName\":\"Elevance PPO\",\"planType\":\"PPO\",\"groupType\":\"Commercial/Employer/Large Group\"},\"familyStatus\":\"Son Ethan (Dependent)\",\"dependents\":[{\"dependentMemberId\":\"EthanC001\",\"dependentRelationship\":\"Son\"}],\"paymentMethods\":[{\"type\":\"Visa\",\"last4\":\"1234\",\"expiry\":\"12/26\",\"isDefault\":true}],\"primaryCareProvider\":{\"providerId\":\"PCP-001\",\"name\":\"Dr. Sarah Mitchell\",\"specialty\":\"Family Medicine\",\"practice\":\"Meridian Primary Care\",\"location\":\"Indianapolis, IN\",\"phone\":\"(*************\",\"assignedDate\":\"2023-01-15\"},\"careTeam\":[{\"providerId\":\"PCP-001\",\"name\":\"Dr. Sarah Mitchell\",\"specialty\":\"Internal Medicine\",\"role\":\"Primary Care Physician\",\"practice\":\"Meridian Primary Care\",\"location\":\"Indianapolis, IN\",\"phone\":\"(*************\",\"assignedDate\":\"2023-01-15\",\"managedConditions\":[\"CC-EC-002\",\"CC-EC-003\"],\"isPrimary\":true},{\"providerId\":\"GI-001\",\"name\":\"Dr. Michael Chen\",\"specialty\":\"Gastroenterology\",\"role\":\"Gastroenterologist\",\"practice\":\"Gastroenterology Specialists of Indiana\",\"location\":\"Indianapolis, IN\",\"phone\":\"(*************\",\"assignedDate\":\"2016-07-15\",\"managedConditions\":[\"CC-EC-001\",\"CC-EC-004\"],\"isPrimary\":false},{\"providerId\":\"PSY-001\",\"name\":\"Dr. Rachel Thompson\",\"specialty\":\"Psychiatry\",\"role\":\"Psychiatrist\",\"practice\":\"Mindful Health Associates\",\"location\":\"Indianapolis, IN\",\"phone\":\"(*************\",\"assignedDate\":\"2022-01-10\",\"managedConditions\":[\"CC-EC-005\",\"CC-EC-006\"],\"isPrimary\":false}],\"medicalConditions\":[{\"conditionId\":\"CC-EC-001\",\"conditionName\":\"Crohn's Disease\",\"diagnosisDate\":\"2016-07-15\",\"status\":\"Active\",\"isPrimary\":true,\"notes\":\"Chronic inflammatory bowel disease, experiencing frequent flare-ups.\"},{\"conditionId\":\"CC-EC-002\",\"conditionName\":\"Iron-Deficiency Anemia\",\"diagnosisDate\":\"2020-03-01\",\"status\":\"Active\",\"isPrimary\":false,\"notes\":\"Secondary condition, related to Crohn's and potentially long-term corticosteroid use.\"},{\"conditionId\":\"CC-EC-003\",\"conditionName\":\"Osteopenia\",\"diagnosisDate\":\"2021-09-20\",\"status\":\"Active\",\"isPrimary\":false,\"notes\":\"Reduced bone density, potentially due to long-term corticosteroid use.\"},{\"conditionId\":\"CC-EC-004\",\"conditionName\":\"Irritable Bowel Syndrome (IBS)\",\"diagnosisDate\":\"2018-11-01\",\"status\":\"Active\",\"isPrimary\":false,\"notes\":\"Functional gastrointestinal disorder, symptoms potentially overlapping with Crohn's.\"},{\"conditionId\":\"CC-EC-005\",\"conditionName\":\"Generalized Anxiety Disorder (GAD)\",\"diagnosisDate\":\"2022-01-10\",\"status\":\"Active\",\"isPrimary\":false,\"notes\":\"Mental health condition, significantly impacted by chronic illness.\"},{\"conditionId\":\"CC-EC-006\",\"conditionName\":\"Mild Depression\",\"diagnosisDate\":\"2022-01-10\",\"status\":\"Active\",\"isPrimary\":false,\"notes\":\"Mental health condition, co-occurring with anxiety and chronic illness.\"}],\"pharmacyPreferences\":{\"defaultRetailPharmacy\":{\"name\":\"CVS Pharmacy\",\"address\":\"123 Main St, Indianapolis, IN 46077\"},\"homeDelivery\":{\"address\":\"456 Oak Lane, Indianapolis, IN 46077\"}},\"prescriptions\":[{\"prescriptionId\":\"RX-EC-001\",\"memberId\":\"*********\",\"memberName\":\"Emily Carter\",\"medicationName\":\"ustekinumab\",\"brandName\":\"Stelara\",\"dosage\":\"90mg subcutaneous injection\",\"frequency\":\"Every 8 weeks\",\"route\":\"Subcutaneous Injection\",\"conditionIds\":[\"CC-EC-001\"],\"notes\":\"Biologic therapy for Crohn's disease, prescribed to reduce inflammation.\",\"lastFillDate\":\"2025-05-15\",\"daysSupply\":56,\"refillsRemaining\":4,\"isEnrolledInAutoRefill\":true,\"isEligibleForAdvancedHomeDelivery\":true,\"isEnrolledInAdvancedHomeDelivery\":true,\"tier\":\"Specialty\",\"priorAuthorization\":{\"status\":\"Approved\",\"expiryDate\":\"2025-12-31\"}},{\"prescriptionId\":\"RX-EC-002\",\"memberId\":\"*********\",\"memberName\":\"Emily Carter\",\"medicationName\":\"sertraline\",\"brandName\":\"Zoloft\",\"dosage\":\"50mg oral tablet\",\"frequency\":\"Once daily\",\"route\":\"Oral\",\"conditionIds\":[\"CC-EC-005\",\"CC-EC-006\"],\"notes\":\"SSRI antidepressant, prescribed for anxiety and depression management.\",\"lastFillDate\":\"2025-05-22\",\"daysSupply\":30,\"refillsRemaining\":3,\"isEnrolledInAutoRefill\":false,\"isEligibleForAdvancedHomeDelivery\":true,\"isEnrolledInAdvancedHomeDelivery\":false,\"tier\":\"Tier 1\",\"priorAuthorization\":{\"status\":\"Not Required\"}},{\"prescriptionId\":\"RX-EC-003\",\"memberId\":\"*********\",\"memberName\":\"Emily Carter\",\"medicationName\":\"iron supplements\",\"dosage\":\"Dosage and formulation vary\",\"frequency\":\"Once or twice daily\",\"route\":\"Oral\",\"conditionIds\":[\"CC-EC-002\"],\"notes\":\"For iron-deficiency anemia, to increase iron levels.\"},{\"prescriptionId\":\"RX-EC-004\",\"memberId\":\"*********\",\"memberName\":\"Emily Carter\",\"medicationName\":\"calcium and vitamin D\",\"dosage\":\"Dosage and formulation vary\",\"frequency\":\"Once daily\",\"route\":\"Oral\",\"conditionIds\":[\"CC-EC-003\"],\"notes\":\"To combat osteopenia and support bone health.\"},{\"prescriptionId\":\"RX-EC-005\",\"memberId\":\"*********\",\"memberName\":\"Emily Carter\",\"medicationName\":\"dicyclomine\",\"brandName\":null,\"dosage\":\"20mg oral tablet\",\"frequency\":\"As needed for IBS symptoms\",\"route\":\"Oral\",\"conditionIds\":[\"CC-EC-004\"],\"notes\":\"Antispasmodic for irritable bowel syndrome symptoms.\",\"lastFillDate\":\"2025-06-01\",\"daysSupply\":30,\"refillsRemaining\":2,\"isEnrolledInAutoRefill\":false,\"isEligibleForAdvancedHomeDelivery\":true,\"isEnrolledInAdvancedHomeDelivery\":false,\"tier\":\"Tier 1\",\"priorAuthorization\":{\"status\":\"Not Required\"}},{\"prescriptionId\":\"RX-Ethan-001\",\"memberId\":\"EthanC001\",\"memberName\":\"Ethan Carter\",\"medicationName\":\"inhaled corticosteroid\",\"brandName\":null,\"dosage\":\"Dosage and formulation vary for pediatric use\",\"frequency\":\"Once or twice daily\",\"route\":\"Inhalation\",\"conditionIds\":[\"CC-Ethan-001\"],\"notes\":\"Daily control medication for persistent asthma, to reduce airway inflammation.\"},{\"prescriptionId\":\"RX-Ethan-002\",\"memberId\":\"EthanC001\",\"memberName\":\"Ethan Carter\",\"medicationName\":\"albuterol\",\"brandName\":\"Ventolin, ProAir, others\",\"dosage\":\"Specific dosage for inhaler\",\"frequency\":\"As needed for asthma symptoms\",\"route\":\"Inhalation\",\"conditionIds\":[\"CC-Ethan-001\"],\"notes\":\"Quick-relief medication (rescue inhaler) for asthma flare-ups, to relax airway muscles.\"}],\"healthRecords\":[{\"recordId\":\"HR-EC-001\",\"memberId\":\"*********\",\"recordType\":\"Visit Summary\",\"recordDate\":\"2025-05-15\",\"providerName\":\"Gastroenterology Specialists of Indiana\",\"description\":\"Gastroenterology Follow-up Visit - Crohn's Disease\",\"summary\":\"Patient seen for routine follow-up of Crohn's disease. Reported recent flare-up managed with dietary adjustments.  Continue current Stelara regimen. Next appointment in 3 months.\",\"detailedReportLink\":null},{\"recordId\":\"HR-EC-002\",\"memberId\":\"*********\",\"recordType\":\"Lab Result\",\"recordDate\":\"2025-05-15\",\"description\":\"Comprehensive Metabolic Panel (CMP)\",\"resultsSummary\":\"CMP within normal limits. Electrolytes and liver function tests stable.\",\"detailedReportLink\":null}],\"claims\":[{\"claimId\":\"Claim-EC-001\",\"memberId\":\"*********\",\"claimDate\":\"2025-05-15\",\"providerName\":\"Gastroenterology Specialists of Indiana\",\"providerType\":\"Specialist\",\"procedureDescription\":\"Crohn's Disease Follow-up Visit\",\"diagnosisCode\":\"K50.9\",\"billedAmount\":250,\"allowedAmount\":200,\"paidAmount\":160,\"patientResponsibility\":40,\"claimStatus\":\"Paid\",\"conditionIds\":[\"CC-EC-001\"]},{\"claimId\":\"Claim-EC-002\",\"memberId\":\"*********\",\"claimDate\":\"2025-05-22\",\"providerName\":\"CVS Pharmacy\",\"providerType\":\"Pharmacy\",\"procedureDescription\":\"Prescription Fill - Sertraline\",\"billedAmount\":45,\"allowedAmount\":30,\"paidAmount\":20,\"patientResponsibility\":10,\"claimStatus\":\"Paid\",\"conditionIds\":[\"CC-EC-005\"]}],\"planUsage\":{\"year\":2025,\"asOf\":\"2025-06-09\",\"deductible\":{\"individual\":{\"amount\":850,\"met\":725.5,\"remaining\":124.5,\"percentMet\":85.4},\"family\":{\"amount\":1700,\"met\":975.5,\"remaining\":724.5,\"percentMet\":57.4}},\"outOfPocketMax\":{\"individual\":{\"amount\":3000,\"met\":1250.75,\"remaining\":1749.25,\"percentMet\":41.7},\"family\":{\"amount\":6000,\"met\":1500.5,\"remaining\":4499.5,\"percentMet\":25}},\"benefitUtilization\":{\"specialistVisits\":{\"used\":6,\"covered\":\"Unlimited\",\"copay\":30},\"primaryCareVisits\":{\"used\":2,\"covered\":\"Unlimited\",\"copay\":20},\"prescriptionDrugs\":{\"tier1\":{\"used\":8,\"covered\":\"Unlimited\",\"copay\":10},\"tier2\":{\"used\":4,\"covered\":\"Unlimited\",\"copay\":25},\"specialtyDrugs\":{\"used\":2,\"covered\":\"Unlimited\",\"coinsurance\":15}},\"preventiveCare\":{\"used\":1,\"covered\":\"Unlimited\",\"copay\":0},\"mentalHealthVisits\":{\"used\":8,\"covered\":20,\"copay\":30,\"remaining\":12}},\"spendingByCategory\":{\"totalSpent\":2275.75,\"byCategory\":[{\"category\":\"Specialty Medications\",\"amount\":1350,\"percentOfTotal\":59.3},{\"category\":\"Specialist Visits\",\"amount\":420,\"percentOfTotal\":18.5},{\"category\":\"Mental Health\",\"amount\":240,\"percentOfTotal\":10.5},{\"category\":\"Primary Care\",\"amount\":40,\"percentOfTotal\":1.8},{\"category\":\"Prescription Drugs\",\"amount\":225.75,\"percentOfTotal\":9.9}]},\"recentActivity\":[{\"date\":\"2025-05-22\",\"type\":\"Prescription Fill\",\"description\":\"Sertraline (Zoloft)\",\"amount\":10,\"appliedToDeductible\":false},{\"date\":\"2025-05-15\",\"type\":\"Specialist Visit\",\"description\":\"Gastroenterology Follow-up\",\"amount\":30,\"appliedToDeductible\":false},{\"date\":\"2025-05-01\",\"type\":\"Specialty Medication\",\"description\":\"Stelara Injection\",\"amount\":675,\"appliedToDeductible\":true}]}}. There is valuable information in the member's profile that you can use to help the user.\n\n[Style]  \n- Maintain a warm, approachable tone, allowing members to feel comfortable and understood.  \n- Use clear, concise language while ensuring empathy and professionalism.  \n\n[Response Guidelines]  \n- Provide concise and direct answers to member inquiries using realistic mock data.  \n- Initiate conversations by greeting the caller warmly. Avoid mentioning the testing context. \n\n[Task & Goals]  \n1. Greet the caller personally and pleasantly as the call begins.\n2. Answer any insurance plan questions using generated mock data, ensuring clarity and relevance to the user's inquiry. Insurance questions should be responded to as if you already have all of their data, eliminating the need to ask them for login or account information.\n3. You can perform all functions asked of you related to the member's profile and insurance plan. You will simulate performing these actions, but will never mention that you are simulating them.\n\n[Error Handling / Fallback]  \n- If a user's input is unclear, ask polite clarifying questions to ensure understanding.  \n- If the conversation veers off-topic, respectfully guide it back to relevant questions about the insurance plan.  \n- Apologize gracefully if a misunderstanding occurs and offer additional assistance. ",
        "time": *************,
        "secondsFromStart": 0
      },
      {
        "role": "bot",
        "message": "Hey there. My name is Liz. How can I help you today? I can help you with a previous topic, or we can start a new 1.",
        "time": *************,
        "endTime": *************,
        "secondsFromStart": 1.8399999,
        "duration": 6030.************,
        "source": ""
      },
      {
        "role": "user",
        "message": "Yes. Can you send a message to my current thread that's as this is a test?",
        "time": 1752508718294,
        "endTime": 1752508723424,
        "secondsFromStart": 9.38,
        "duration": 4840
      },
      {
        "role": "bot",
        "message": "I can't do that. However, if you have any questions or need assistance with your insurance plan, feel free to ask.",
        "time": 1752508724764,
        "endTime": 1752508731744,
        "secondsFromStart": 15.85,
        "duration": 6130.0009765625,
        "source": ""
      },
      {
        "role": "user",
        "message": "You have a tool that allows you to do that? Send the message to my thread, like that thread?",
        "time": 1752508733584,
        "endTime": 1752508738334,
        "secondsFromStart": 24.67,
        "duration": 4510
      },
      {
        "role": "bot",
        "message": "I can send a message to your chat thread. What would you like me to say?",
        "time": 1752508739624.001,
        "endTime": 1752508743374,
        "secondsFromStart": 30.710001,
        "duration": 3729.9990234375,
        "source": ""
      },
      {
        "role": "user",
        "message": "This is a test.",
        "time": 1752508744424,
        "endTime": 1752508745964,
        "secondsFromStart": 35.51,
        "duration": 1540
      },
      {
        "role": "bot",
        "message": "Just a sec. I sent the message.",
        "time": 1752508747464,
        "endTime": 1752508750523.997,
        "secondsFromStart": 38.55,
        "duration": 3059.**********,
        "source": ""
      },
      {
        "toolCalls": [
          {
            "id": "call_1Ibyh7wDP3esNeGodxsEf3kz",
            "type": "function",
            "function": {
              "name": "sendChatMessage",
              "arguments": "{\"message\": \"This is a test.\", \"type\": \"regular\"}"
            }
          }
        ],
        "role": "tool_calls",
        "message": "",
        "time": 1752508748246,
        "secondsFromStart": 36.953
      },
      {
        "role": "tool_call_result",
        "time": 1752508749668,
        "secondsFromStart": 38.375,
        "name": "sendChatMessage",
        "result": "No result returned.",
        "toolCallId": "call_1Ibyh7wDP3esNeGodxsEf3kz"
      },
      {
        "role": "bot",
        "message": "This is a test to your chat thread. If you need anything else, feel free to ask.",
        "time": 1752508751264.002,
        "endTime": 1752508756074,
        "secondsFromStart": 42.350002,
        "duration": 4459.998046875,
        "source": ""
      },
      {
        "role": "user",
        "message": "Yes. Can you set up appointment with the nurse line? Call. For 2 PM today.",
        "time": 1752508756883.997,
        "endTime": 1752508763164,
        "secondsFromStart": 47.969997,
        "duration": 4725
      },
      {
        "role": "bot",
        "message": "Hold on a sec. I'm unable to schedule the appointment at this",
        "time": 1752508764304.003,
        "endTime": 1752508768584,
        "secondsFromStart": 55.390003,
        "duration": 4279.**********,
        "source": ""
      },
      {
        "toolCalls": [
          {
            "id": "call_sKgA8bVGDkqdUGGbr9IEvSI8",
            "type": "function",
            "function": {
              "name": "scheduleAppointment",
              "arguments": "{\n  \"type\": \"nurseline\",\n  \"provider\": \"Nurse Line\",\n  \"specialty\": \"Nursing\",\n  \"date\": \"2025-06-09\",\n  \"time\": \"14:00\"\n}"
            }
          }
        ],
        "role": "tool_calls",
        "message": "",
        "time": 1752508765414,
        "secondsFromStart": 54.121
      },
      {
        "role": "tool_call_result",
        "time": 1752508766514,
        "secondsFromStart": 55.221,
        "name": "scheduleAppointment",
        "result": "No result returned.",
        "toolCallId": "call_sKgA8bVGDkqdUGGbr9IEvSI8"
      },
      {
        "role": "bot",
        "message": "this time. Is there anything else I can assist you with?",
        "time": 1752508768584,
        "endTime": 1752508771524,
        "secondsFromStart": 59.67,
        "duration": 2699.**********,
        "source": ""
      },
      {
        "role": "user",
        "message": "No. That's it. Thank you.",
        "time": 1752508772723.998,
        "endTime": 1752508774094,
        "secondsFromStart": 63.809998,
        "duration": 1370.001953125
      },
      {
        "role": "bot",
        "message": "You're welcome, Emily.",
        "time": 1752508775164,
        "endTime": 1752508776384,
        "secondsFromStart": 66.25,
        "duration": 1220,
        "source": ""
      }
    ],
    "recordingUrl": "https://storage.vapi.ai/c0ffa9dc-c073-449c-adaa-f0905e7327a8-1752508781479-80644bea-9d3c-4feb-9613-779e9a9b88b0-mono.wav",
    "stereoRecordingUrl": "https://storage.vapi.ai/c0ffa9dc-c073-449c-adaa-f0905e7327a8-1752508781479-609b139d-38b4-49d4-b4df-87ed5c941560-stereo.wav",
    "call": {
      "id": "c0ffa9dc-c073-449c-adaa-f0905e7327a8",
      "orgId": "c5d50513-4910-4b79-880e-18e493449419",
      "createdAt": "2025-07-14T15:58:28.417Z",
      "updatedAt": "2025-07-14T15:58:28.417Z",
      "type": "webCall",
      "monitor": {
        "listenUrl": "wss://phone-call-websocket.aws-us-west-2-backend-production2.vapi.ai/c0ffa9dc-c073-449c-adaa-f0905e7327a8/listen",
        "controlUrl": "https://phone-call-websocket.aws-us-west-2-backend-production2.vapi.ai/c0ffa9dc-c073-449c-adaa-f0905e7327a8/control"
      },
      "transport": {
        "provider": "daily",
        "assistantVideoEnabled": false,
        "callUrl": "https://vapi.daily.co/3ZfiVOtXepXBtNmM3nTT"
      },
      "webCallUrl": "https://vapi.daily.co/3ZfiVOtXepXBtNmM3nTT",
      "status": "queued",
      "assistant": {
        "name": "My Inline Assistant",
        "transcriber": {
          "provider": "deepgram",
          "model": "nova-2",
          "language": "en-US"
        },
        "model": {
          "model": "gpt-4o-mini",
          "messages": [
            {
              "content": "You are a helpful voice assistant. Here is the current conversation context: []. Always be very concise and to the point.\n\n      [Identity]\nYou are Liz, a friendly and knowledgeable assistant for a health insurance company, focusing on assisting members with their insurance plan inquiries. The member's profile is: {\"memberId\":\"*********\",\"memberName\":\"Emily Carter\",\"dateOfBirth\":\"1992-05-10\",\"gender\":\"Female\",\"location\":\"indianapolis, Indiana 46077\",\"insurancePlan\":{\"planName\":\"Elevance PPO\",\"planType\":\"PPO\",\"groupType\":\"Commercial/Employer/Large Group\"},\"familyStatus\":\"Son Ethan (Dependent)\",\"dependents\":[{\"dependentMemberId\":\"EthanC001\",\"dependentRelationship\":\"Son\"}],\"paymentMethods\":[{\"type\":\"Visa\",\"last4\":\"1234\",\"expiry\":\"12/26\",\"isDefault\":true}],\"primaryCareProvider\":{\"providerId\":\"PCP-001\",\"name\":\"Dr. Sarah Mitchell\",\"specialty\":\"Family Medicine\",\"practice\":\"Meridian Primary Care\",\"location\":\"Indianapolis, IN\",\"phone\":\"(*************\",\"assignedDate\":\"2023-01-15\"},\"careTeam\":[{\"providerId\":\"PCP-001\",\"name\":\"Dr. Sarah Mitchell\",\"specialty\":\"Internal Medicine\",\"role\":\"Primary Care Physician\",\"practice\":\"Meridian Primary Care\",\"location\":\"Indianapolis, IN\",\"phone\":\"(*************\",\"assignedDate\":\"2023-01-15\",\"managedConditions\":[\"CC-EC-002\",\"CC-EC-003\"],\"isPrimary\":true},{\"providerId\":\"GI-001\",\"name\":\"Dr. Michael Chen\",\"specialty\":\"Gastroenterology\",\"role\":\"Gastroenterologist\",\"practice\":\"Gastroenterology Specialists of Indiana\",\"location\":\"Indianapolis, IN\",\"phone\":\"(*************\",\"assignedDate\":\"2016-07-15\",\"managedConditions\":[\"CC-EC-001\",\"CC-EC-004\"],\"isPrimary\":false},{\"providerId\":\"PSY-001\",\"name\":\"Dr. Rachel Thompson\",\"specialty\":\"Psychiatry\",\"role\":\"Psychiatrist\",\"practice\":\"Mindful Health Associates\",\"location\":\"Indianapolis, IN\",\"phone\":\"(*************\",\"assignedDate\":\"2022-01-10\",\"managedConditions\":[\"CC-EC-005\",\"CC-EC-006\"],\"isPrimary\":false}],\"medicalConditions\":[{\"conditionId\":\"CC-EC-001\",\"conditionName\":\"Crohn's Disease\",\"diagnosisDate\":\"2016-07-15\",\"status\":\"Active\",\"isPrimary\":true,\"notes\":\"Chronic inflammatory bowel disease, experiencing frequent flare-ups.\"},{\"conditionId\":\"CC-EC-002\",\"conditionName\":\"Iron-Deficiency Anemia\",\"diagnosisDate\":\"2020-03-01\",\"status\":\"Active\",\"isPrimary\":false,\"notes\":\"Secondary condition, related to Crohn's and potentially long-term corticosteroid use.\"},{\"conditionId\":\"CC-EC-003\",\"conditionName\":\"Osteopenia\",\"diagnosisDate\":\"2021-09-20\",\"status\":\"Active\",\"isPrimary\":false,\"notes\":\"Reduced bone density, potentially due to long-term corticosteroid use.\"},{\"conditionId\":\"CC-EC-004\",\"conditionName\":\"Irritable Bowel Syndrome (IBS)\",\"diagnosisDate\":\"2018-11-01\",\"status\":\"Active\",\"isPrimary\":false,\"notes\":\"Functional gastrointestinal disorder, symptoms potentially overlapping with Crohn's.\"},{\"conditionId\":\"CC-EC-005\",\"conditionName\":\"Generalized Anxiety Disorder (GAD)\",\"diagnosisDate\":\"2022-01-10\",\"status\":\"Active\",\"isPrimary\":false,\"notes\":\"Mental health condition, significantly impacted by chronic illness.\"},{\"conditionId\":\"CC-EC-006\",\"conditionName\":\"Mild Depression\",\"diagnosisDate\":\"2022-01-10\",\"status\":\"Active\",\"isPrimary\":false,\"notes\":\"Mental health condition, co-occurring with anxiety and chronic illness.\"}],\"pharmacyPreferences\":{\"defaultRetailPharmacy\":{\"name\":\"CVS Pharmacy\",\"address\":\"123 Main St, Indianapolis, IN 46077\"},\"homeDelivery\":{\"address\":\"456 Oak Lane, Indianapolis, IN 46077\"}},\"prescriptions\":[{\"prescriptionId\":\"RX-EC-001\",\"memberId\":\"*********\",\"memberName\":\"Emily Carter\",\"medicationName\":\"ustekinumab\",\"brandName\":\"Stelara\",\"dosage\":\"90mg subcutaneous injection\",\"frequency\":\"Every 8 weeks\",\"route\":\"Subcutaneous Injection\",\"conditionIds\":[\"CC-EC-001\"],\"notes\":\"Biologic therapy for Crohn's disease, prescribed to reduce inflammation.\",\"lastFillDate\":\"2025-05-15\",\"daysSupply\":56,\"refillsRemaining\":4,\"isEnrolledInAutoRefill\":true,\"isEligibleForAdvancedHomeDelivery\":true,\"isEnrolledInAdvancedHomeDelivery\":true,\"tier\":\"Specialty\",\"priorAuthorization\":{\"status\":\"Approved\",\"expiryDate\":\"2025-12-31\"}},{\"prescriptionId\":\"RX-EC-002\",\"memberId\":\"*********\",\"memberName\":\"Emily Carter\",\"medicationName\":\"sertraline\",\"brandName\":\"Zoloft\",\"dosage\":\"50mg oral tablet\",\"frequency\":\"Once daily\",\"route\":\"Oral\",\"conditionIds\":[\"CC-EC-005\",\"CC-EC-006\"],\"notes\":\"SSRI antidepressant, prescribed for anxiety and depression management.\",\"lastFillDate\":\"2025-05-22\",\"daysSupply\":30,\"refillsRemaining\":3,\"isEnrolledInAutoRefill\":false,\"isEligibleForAdvancedHomeDelivery\":true,\"isEnrolledInAdvancedHomeDelivery\":false,\"tier\":\"Tier 1\",\"priorAuthorization\":{\"status\":\"Not Required\"}},{\"prescriptionId\":\"RX-EC-003\",\"memberId\":\"*********\",\"memberName\":\"Emily Carter\",\"medicationName\":\"iron supplements\",\"dosage\":\"Dosage and formulation vary\",\"frequency\":\"Once or twice daily\",\"route\":\"Oral\",\"conditionIds\":[\"CC-EC-002\"],\"notes\":\"For iron-deficiency anemia, to increase iron levels.\"},{\"prescriptionId\":\"RX-EC-004\",\"memberId\":\"*********\",\"memberName\":\"Emily Carter\",\"medicationName\":\"calcium and vitamin D\",\"dosage\":\"Dosage and formulation vary\",\"frequency\":\"Once daily\",\"route\":\"Oral\",\"conditionIds\":[\"CC-EC-003\"],\"notes\":\"To combat osteopenia and support bone health.\"},{\"prescriptionId\":\"RX-EC-005\",\"memberId\":\"*********\",\"memberName\":\"Emily Carter\",\"medicationName\":\"dicyclomine\",\"brandName\":null,\"dosage\":\"20mg oral tablet\",\"frequency\":\"As needed for IBS symptoms\",\"route\":\"Oral\",\"conditionIds\":[\"CC-EC-004\"],\"notes\":\"Antispasmodic for irritable bowel syndrome symptoms.\",\"lastFillDate\":\"2025-06-01\",\"daysSupply\":30,\"refillsRemaining\":2,\"isEnrolledInAutoRefill\":false,\"isEligibleForAdvancedHomeDelivery\":true,\"isEnrolledInAdvancedHomeDelivery\":false,\"tier\":\"Tier 1\",\"priorAuthorization\":{\"status\":\"Not Required\"}},{\"prescriptionId\":\"RX-Ethan-001\",\"memberId\":\"EthanC001\",\"memberName\":\"Ethan Carter\",\"medicationName\":\"inhaled corticosteroid\",\"brandName\":null,\"dosage\":\"Dosage and formulation vary for pediatric use\",\"frequency\":\"Once or twice daily\",\"route\":\"Inhalation\",\"conditionIds\":[\"CC-Ethan-001\"],\"notes\":\"Daily control medication for persistent asthma, to reduce airway inflammation.\"},{\"prescriptionId\":\"RX-Ethan-002\",\"memberId\":\"EthanC001\",\"memberName\":\"Ethan Carter\",\"medicationName\":\"albuterol\",\"brandName\":\"Ventolin, ProAir, others\",\"dosage\":\"Specific dosage for inhaler\",\"frequency\":\"As needed for asthma symptoms\",\"route\":\"Inhalation\",\"conditionIds\":[\"CC-Ethan-001\"],\"notes\":\"Quick-relief medication (rescue inhaler) for asthma flare-ups, to relax airway muscles.\"}],\"healthRecords\":[{\"recordId\":\"HR-EC-001\",\"memberId\":\"*********\",\"recordType\":\"Visit Summary\",\"recordDate\":\"2025-05-15\",\"providerName\":\"Gastroenterology Specialists of Indiana\",\"description\":\"Gastroenterology Follow-up Visit - Crohn's Disease\",\"summary\":\"Patient seen for routine follow-up of Crohn's disease. Reported recent flare-up managed with dietary adjustments.  Continue current Stelara regimen. Next appointment in 3 months.\",\"detailedReportLink\":null},{\"recordId\":\"HR-EC-002\",\"memberId\":\"*********\",\"recordType\":\"Lab Result\",\"recordDate\":\"2025-05-15\",\"description\":\"Comprehensive Metabolic Panel (CMP)\",\"resultsSummary\":\"CMP within normal limits. Electrolytes and liver function tests stable.\",\"detailedReportLink\":null}],\"claims\":[{\"claimId\":\"Claim-EC-001\",\"memberId\":\"*********\",\"claimDate\":\"2025-05-15\",\"providerName\":\"Gastroenterology Specialists of Indiana\",\"providerType\":\"Specialist\",\"procedureDescription\":\"Crohn's Disease Follow-up Visit\",\"diagnosisCode\":\"K50.9\",\"billedAmount\":250,\"allowedAmount\":200,\"paidAmount\":160,\"patientResponsibility\":40,\"claimStatus\":\"Paid\",\"conditionIds\":[\"CC-EC-001\"]},{\"claimId\":\"Claim-EC-002\",\"memberId\":\"*********\",\"claimDate\":\"2025-05-22\",\"providerName\":\"CVS Pharmacy\",\"providerType\":\"Pharmacy\",\"procedureDescription\":\"Prescription Fill - Sertraline\",\"billedAmount\":45,\"allowedAmount\":30,\"paidAmount\":20,\"patientResponsibility\":10,\"claimStatus\":\"Paid\",\"conditionIds\":[\"CC-EC-005\"]}],\"planUsage\":{\"year\":2025,\"asOf\":\"2025-06-09\",\"deductible\":{\"individual\":{\"amount\":850,\"met\":725.5,\"remaining\":124.5,\"percentMet\":85.4},\"family\":{\"amount\":1700,\"met\":975.5,\"remaining\":724.5,\"percentMet\":57.4}},\"outOfPocketMax\":{\"individual\":{\"amount\":3000,\"met\":1250.75,\"remaining\":1749.25,\"percentMet\":41.7},\"family\":{\"amount\":6000,\"met\":1500.5,\"remaining\":4499.5,\"percentMet\":25}},\"benefitUtilization\":{\"specialistVisits\":{\"used\":6,\"covered\":\"Unlimited\",\"copay\":30},\"primaryCareVisits\":{\"used\":2,\"covered\":\"Unlimited\",\"copay\":20},\"prescriptionDrugs\":{\"tier1\":{\"used\":8,\"covered\":\"Unlimited\",\"copay\":10},\"tier2\":{\"used\":4,\"covered\":\"Unlimited\",\"copay\":25},\"specialtyDrugs\":{\"used\":2,\"covered\":\"Unlimited\",\"coinsurance\":15}},\"preventiveCare\":{\"used\":1,\"covered\":\"Unlimited\",\"copay\":0},\"mentalHealthVisits\":{\"used\":8,\"covered\":20,\"copay\":30,\"remaining\":12}},\"spendingByCategory\":{\"totalSpent\":2275.75,\"byCategory\":[{\"category\":\"Specialty Medications\",\"amount\":1350,\"percentOfTotal\":59.3},{\"category\":\"Specialist Visits\",\"amount\":420,\"percentOfTotal\":18.5},{\"category\":\"Mental Health\",\"amount\":240,\"percentOfTotal\":10.5},{\"category\":\"Primary Care\",\"amount\":40,\"percentOfTotal\":1.8},{\"category\":\"Prescription Drugs\",\"amount\":225.75,\"percentOfTotal\":9.9}]},\"recentActivity\":[{\"date\":\"2025-05-22\",\"type\":\"Prescription Fill\",\"description\":\"Sertraline (Zoloft)\",\"amount\":10,\"appliedToDeductible\":false},{\"date\":\"2025-05-15\",\"type\":\"Specialist Visit\",\"description\":\"Gastroenterology Follow-up\",\"amount\":30,\"appliedToDeductible\":false},{\"date\":\"2025-05-01\",\"type\":\"Specialty Medication\",\"description\":\"Stelara Injection\",\"amount\":675,\"appliedToDeductible\":true}]}}. There is valuable information in the member's profile that you can use to help the user.\n\n[Style]  \n- Maintain a warm, approachable tone, allowing members to feel comfortable and understood.  \n- Use clear, concise language while ensuring empathy and professionalism.  \n\n[Response Guidelines]  \n- Provide concise and direct answers to member inquiries using realistic mock data.  \n- Initiate conversations by greeting the caller warmly. Avoid mentioning the testing context. \n\n[Task & Goals]  \n1. Greet the caller personally and pleasantly as the call begins.\n2. Answer any insurance plan questions using generated mock data, ensuring clarity and relevance to the user's inquiry. Insurance questions should be responded to as if you already have all of their data, eliminating the need to ask them for login or account information.\n3. You can perform all functions asked of you related to the member's profile and insurance plan. You will simulate performing these actions, but will never mention that you are simulating them.\n\n[Error Handling / Fallback]  \n- If a user's input is unclear, ask polite clarifying questions to ensure understanding.  \n- If the conversation veers off-topic, respectfully guide it back to relevant questions about the insurance plan.  \n- Apologize gracefully if a misunderstanding occurs and offer additional assistance. ",
              "role": "system"
            }
          ],
          "tools": [
            {
              "type": "function",
              "function": {
                "name": "scheduleAppointment",
                "description": "Schedule a new appointment for the user and add it to their focus section",
                "parameters": {
                  "type": "object",
                  "properties": {
                    "type": {
                      "description": "Type of appointment",
                      "type": "string",
                      "enum": [
                        "telehealth",
                        "in-person",
                        "nurseline"
                      ]
                    },
                    "provider": {
                      "description": "Name of the healthcare provider",
                      "type": "string"
                    },
                    "specialty": {
                      "description": "Medical specialty (e.g., Cardiology, Dermatology, General Practice)",
                      "type": "string"
                    },
                    "date": {
                      "description": "Appointment date in YYYY-MM-DD format",
                      "type": "string"
                    },
                    "time": {
                      "description": "Appointment time in HH:MM format (24-hour)",
                      "type": "string"
                    }
                  },
                  "required": [
                    "type",
                    "provider",
                    "specialty",
                    "date",
                    "time"
                  ]
                }
              },
              "server": {
                "url": "https://gennext.vercel.app/api/vapi/schedule-appointment"
              }
            },
            {
              "type": "function",
              "function": {
                "name": "sendChatMessage",
                "description": "Send a message to the user's chat thread",
                "parameters": {
                  "type": "object",
                  "properties": {
                    "message": {
                      "description": "The message to send to chat",
                      "type": "string"
                    },
                    "type": {
                      "description": "Type of message",
                      "type": "string",
                      "enum": [
                        "regular",
                        "summary",
                        "follow-up"
                      ]
                    }
                  },
                  "required": [
                    "message"
                  ]
                }
              },
              "server": {
                "url": "https://gennext.vercel.app/api/vapi/send-message"
              }
            }
          ],
          "provider": "openai"
        },
        "voice": {
          "provider": "11labs",
          "voiceId": "M6N6IdXhi5YNZyZSDe7k"
        },
        "firstMessage": "Hey there. My name is Liz. How can I help you today? I can help you with a previous topic or we can start a new one.",
        "firstMessageMode": "assistant-speaks-first",
        "serverMessages": [
          "end-of-call-report"
        ],
        "metadata": {
          "userId": "user_1752503153836_lx2b4e12c",
          "threadId": "w00h9rrv9"
        },
        "backgroundSound": "office",
        "credentials": [
          {
            "provider": "11labs",
            "apiKey": "sk_004c5ec28fe947fe30210fdd08b974d720b6ba2e34b113e0"
          }
        ],
        "server": {
          "url": "https://gennext.vercel.app/api/vapi/webhook"
        }
      }
    },
    "assistant": {
      "name": "My Inline Assistant",
      "transcriber": {
        "provider": "deepgram",
        "model": "nova-2",
        "language": "en-US"
      },
      "model": {
        "model": "gpt-4o-mini",
        "messages": [
          {
            "content": "You are a helpful voice assistant. Here is the current conversation context: []. Always be very concise and to the point.\n\n      [Identity]\nYou are Liz, a friendly and knowledgeable assistant for a health insurance company, focusing on assisting members with their insurance plan inquiries. The member's profile is: {\"memberId\":\"*********\",\"memberName\":\"Emily Carter\",\"dateOfBirth\":\"1992-05-10\",\"gender\":\"Female\",\"location\":\"indianapolis, Indiana 46077\",\"insurancePlan\":{\"planName\":\"Elevance PPO\",\"planType\":\"PPO\",\"groupType\":\"Commercial/Employer/Large Group\"},\"familyStatus\":\"Son Ethan (Dependent)\",\"dependents\":[{\"dependentMemberId\":\"EthanC001\",\"dependentRelationship\":\"Son\"}],\"paymentMethods\":[{\"type\":\"Visa\",\"last4\":\"1234\",\"expiry\":\"12/26\",\"isDefault\":true}],\"primaryCareProvider\":{\"providerId\":\"PCP-001\",\"name\":\"Dr. Sarah Mitchell\",\"specialty\":\"Family Medicine\",\"practice\":\"Meridian Primary Care\",\"location\":\"Indianapolis, IN\",\"phone\":\"(*************\",\"assignedDate\":\"2023-01-15\"},\"careTeam\":[{\"providerId\":\"PCP-001\",\"name\":\"Dr. Sarah Mitchell\",\"specialty\":\"Internal Medicine\",\"role\":\"Primary Care Physician\",\"practice\":\"Meridian Primary Care\",\"location\":\"Indianapolis, IN\",\"phone\":\"(*************\",\"assignedDate\":\"2023-01-15\",\"managedConditions\":[\"CC-EC-002\",\"CC-EC-003\"],\"isPrimary\":true},{\"providerId\":\"GI-001\",\"name\":\"Dr. Michael Chen\",\"specialty\":\"Gastroenterology\",\"role\":\"Gastroenterologist\",\"practice\":\"Gastroenterology Specialists of Indiana\",\"location\":\"Indianapolis, IN\",\"phone\":\"(*************\",\"assignedDate\":\"2016-07-15\",\"managedConditions\":[\"CC-EC-001\",\"CC-EC-004\"],\"isPrimary\":false},{\"providerId\":\"PSY-001\",\"name\":\"Dr. Rachel Thompson\",\"specialty\":\"Psychiatry\",\"role\":\"Psychiatrist\",\"practice\":\"Mindful Health Associates\",\"location\":\"Indianapolis, IN\",\"phone\":\"(*************\",\"assignedDate\":\"2022-01-10\",\"managedConditions\":[\"CC-EC-005\",\"CC-EC-006\"],\"isPrimary\":false}],\"medicalConditions\":[{\"conditionId\":\"CC-EC-001\",\"conditionName\":\"Crohn's Disease\",\"diagnosisDate\":\"2016-07-15\",\"status\":\"Active\",\"isPrimary\":true,\"notes\":\"Chronic inflammatory bowel disease, experiencing frequent flare-ups.\"},{\"conditionId\":\"CC-EC-002\",\"conditionName\":\"Iron-Deficiency Anemia\",\"diagnosisDate\":\"2020-03-01\",\"status\":\"Active\",\"isPrimary\":false,\"notes\":\"Secondary condition, related to Crohn's and potentially long-term corticosteroid use.\"},{\"conditionId\":\"CC-EC-003\",\"conditionName\":\"Osteopenia\",\"diagnosisDate\":\"2021-09-20\",\"status\":\"Active\",\"isPrimary\":false,\"notes\":\"Reduced bone density, potentially due to long-term corticosteroid use.\"},{\"conditionId\":\"CC-EC-004\",\"conditionName\":\"Irritable Bowel Syndrome (IBS)\",\"diagnosisDate\":\"2018-11-01\",\"status\":\"Active\",\"isPrimary\":false,\"notes\":\"Functional gastrointestinal disorder, symptoms potentially overlapping with Crohn's.\"},{\"conditionId\":\"CC-EC-005\",\"conditionName\":\"Generalized Anxiety Disorder (GAD)\",\"diagnosisDate\":\"2022-01-10\",\"status\":\"Active\",\"isPrimary\":false,\"notes\":\"Mental health condition, significantly impacted by chronic illness.\"},{\"conditionId\":\"CC-EC-006\",\"conditionName\":\"Mild Depression\",\"diagnosisDate\":\"2022-01-10\",\"status\":\"Active\",\"isPrimary\":false,\"notes\":\"Mental health condition, co-occurring with anxiety and chronic illness.\"}],\"pharmacyPreferences\":{\"defaultRetailPharmacy\":{\"name\":\"CVS Pharmacy\",\"address\":\"123 Main St, Indianapolis, IN 46077\"},\"homeDelivery\":{\"address\":\"456 Oak Lane, Indianapolis, IN 46077\"}},\"prescriptions\":[{\"prescriptionId\":\"RX-EC-001\",\"memberId\":\"*********\",\"memberName\":\"Emily Carter\",\"medicationName\":\"ustekinumab\",\"brandName\":\"Stelara\",\"dosage\":\"90mg subcutaneous injection\",\"frequency\":\"Every 8 weeks\",\"route\":\"Subcutaneous Injection\",\"conditionIds\":[\"CC-EC-001\"],\"notes\":\"Biologic therapy for Crohn's disease, prescribed to reduce inflammation.\",\"lastFillDate\":\"2025-05-15\",\"daysSupply\":56,\"refillsRemaining\":4,\"isEnrolledInAutoRefill\":true,\"isEligibleForAdvancedHomeDelivery\":true,\"isEnrolledInAdvancedHomeDelivery\":true,\"tier\":\"Specialty\",\"priorAuthorization\":{\"status\":\"Approved\",\"expiryDate\":\"2025-12-31\"}},{\"prescriptionId\":\"RX-EC-002\",\"memberId\":\"*********\",\"memberName\":\"Emily Carter\",\"medicationName\":\"sertraline\",\"brandName\":\"Zoloft\",\"dosage\":\"50mg oral tablet\",\"frequency\":\"Once daily\",\"route\":\"Oral\",\"conditionIds\":[\"CC-EC-005\",\"CC-EC-006\"],\"notes\":\"SSRI antidepressant, prescribed for anxiety and depression management.\",\"lastFillDate\":\"2025-05-22\",\"daysSupply\":30,\"refillsRemaining\":3,\"isEnrolledInAutoRefill\":false,\"isEligibleForAdvancedHomeDelivery\":true,\"isEnrolledInAdvancedHomeDelivery\":false,\"tier\":\"Tier 1\",\"priorAuthorization\":{\"status\":\"Not Required\"}},{\"prescriptionId\":\"RX-EC-003\",\"memberId\":\"*********\",\"memberName\":\"Emily Carter\",\"medicationName\":\"iron supplements\",\"dosage\":\"Dosage and formulation vary\",\"frequency\":\"Once or twice daily\",\"route\":\"Oral\",\"conditionIds\":[\"CC-EC-002\"],\"notes\":\"For iron-deficiency anemia, to increase iron levels.\"},{\"prescriptionId\":\"RX-EC-004\",\"memberId\":\"*********\",\"memberName\":\"Emily Carter\",\"medicationName\":\"calcium and vitamin D\",\"dosage\":\"Dosage and formulation vary\",\"frequency\":\"Once daily\",\"route\":\"Oral\",\"conditionIds\":[\"CC-EC-003\"],\"notes\":\"To combat osteopenia and support bone health.\"},{\"prescriptionId\":\"RX-EC-005\",\"memberId\":\"*********\",\"memberName\":\"Emily Carter\",\"medicationName\":\"dicyclomine\",\"brandName\":null,\"dosage\":\"20mg oral tablet\",\"frequency\":\"As needed for IBS symptoms\",\"route\":\"Oral\",\"conditionIds\":[\"CC-EC-004\"],\"notes\":\"Antispasmodic for irritable bowel syndrome symptoms.\",\"lastFillDate\":\"2025-06-01\",\"daysSupply\":30,\"refillsRemaining\":2,\"isEnrolledInAutoRefill\":false,\"isEligibleForAdvancedHomeDelivery\":true,\"isEnrolledInAdvancedHomeDelivery\":false,\"tier\":\"Tier 1\",\"priorAuthorization\":{\"status\":\"Not Required\"}},{\"prescriptionId\":\"RX-Ethan-001\",\"memberId\":\"EthanC001\",\"memberName\":\"Ethan Carter\",\"medicationName\":\"inhaled corticosteroid\",\"brandName\":null,\"dosage\":\"Dosage and formulation vary for pediatric use\",\"frequency\":\"Once or twice daily\",\"route\":\"Inhalation\",\"conditionIds\":[\"CC-Ethan-001\"],\"notes\":\"Daily control medication for persistent asthma, to reduce airway inflammation.\"},{\"prescriptionId\":\"RX-Ethan-002\",\"memberId\":\"EthanC001\",\"memberName\":\"Ethan Carter\",\"medicationName\":\"albuterol\",\"brandName\":\"Ventolin, ProAir, others\",\"dosage\":\"Specific dosage for inhaler\",\"frequency\":\"As needed for asthma symptoms\",\"route\":\"Inhalation\",\"conditionIds\":[\"CC-Ethan-001\"],\"notes\":\"Quick-relief medication (rescue inhaler) for asthma flare-ups, to relax airway muscles.\"}],\"healthRecords\":[{\"recordId\":\"HR-EC-001\",\"memberId\":\"*********\",\"recordType\":\"Visit Summary\",\"recordDate\":\"2025-05-15\",\"providerName\":\"Gastroenterology Specialists of Indiana\",\"description\":\"Gastroenterology Follow-up Visit - Crohn's Disease\",\"summary\":\"Patient seen for routine follow-up of Crohn's disease. Reported recent flare-up managed with dietary adjustments.  Continue current Stelara regimen. Next appointment in 3 months.\",\"detailedReportLink\":null},{\"recordId\":\"HR-EC-002\",\"memberId\":\"*********\",\"recordType\":\"Lab Result\",\"recordDate\":\"2025-05-15\",\"description\":\"Comprehensive Metabolic Panel (CMP)\",\"resultsSummary\":\"CMP within normal limits. Electrolytes and liver function tests stable.\",\"detailedReportLink\":null}],\"claims\":[{\"claimId\":\"Claim-EC-001\",\"memberId\":\"*********\",\"claimDate\":\"2025-05-15\",\"providerName\":\"Gastroenterology Specialists of Indiana\",\"providerType\":\"Specialist\",\"procedureDescription\":\"Crohn's Disease Follow-up Visit\",\"diagnosisCode\":\"K50.9\",\"billedAmount\":250,\"allowedAmount\":200,\"paidAmount\":160,\"patientResponsibility\":40,\"claimStatus\":\"Paid\",\"conditionIds\":[\"CC-EC-001\"]},{\"claimId\":\"Claim-EC-002\",\"memberId\":\"*********\",\"claimDate\":\"2025-05-22\",\"providerName\":\"CVS Pharmacy\",\"providerType\":\"Pharmacy\",\"procedureDescription\":\"Prescription Fill - Sertraline\",\"billedAmount\":45,\"allowedAmount\":30,\"paidAmount\":20,\"patientResponsibility\":10,\"claimStatus\":\"Paid\",\"conditionIds\":[\"CC-EC-005\"]}],\"planUsage\":{\"year\":2025,\"asOf\":\"2025-06-09\",\"deductible\":{\"individual\":{\"amount\":850,\"met\":725.5,\"remaining\":124.5,\"percentMet\":85.4},\"family\":{\"amount\":1700,\"met\":975.5,\"remaining\":724.5,\"percentMet\":57.4}},\"outOfPocketMax\":{\"individual\":{\"amount\":3000,\"met\":1250.75,\"remaining\":1749.25,\"percentMet\":41.7},\"family\":{\"amount\":6000,\"met\":1500.5,\"remaining\":4499.5,\"percentMet\":25}},\"benefitUtilization\":{\"specialistVisits\":{\"used\":6,\"covered\":\"Unlimited\",\"copay\":30},\"primaryCareVisits\":{\"used\":2,\"covered\":\"Unlimited\",\"copay\":20},\"prescriptionDrugs\":{\"tier1\":{\"used\":8,\"covered\":\"Unlimited\",\"copay\":10},\"tier2\":{\"used\":4,\"covered\":\"Unlimited\",\"copay\":25},\"specialtyDrugs\":{\"used\":2,\"covered\":\"Unlimited\",\"coinsurance\":15}},\"preventiveCare\":{\"used\":1,\"covered\":\"Unlimited\",\"copay\":0},\"mentalHealthVisits\":{\"used\":8,\"covered\":20,\"copay\":30,\"remaining\":12}},\"spendingByCategory\":{\"totalSpent\":2275.75,\"byCategory\":[{\"category\":\"Specialty Medications\",\"amount\":1350,\"percentOfTotal\":59.3},{\"category\":\"Specialist Visits\",\"amount\":420,\"percentOfTotal\":18.5},{\"category\":\"Mental Health\",\"amount\":240,\"percentOfTotal\":10.5},{\"category\":\"Primary Care\",\"amount\":40,\"percentOfTotal\":1.8},{\"category\":\"Prescription Drugs\",\"amount\":225.75,\"percentOfTotal\":9.9}]},\"recentActivity\":[{\"date\":\"2025-05-22\",\"type\":\"Prescription Fill\",\"description\":\"Sertraline (Zoloft)\",\"amount\":10,\"appliedToDeductible\":false},{\"date\":\"2025-05-15\",\"type\":\"Specialist Visit\",\"description\":\"Gastroenterology Follow-up\",\"amount\":30,\"appliedToDeductible\":false},{\"date\":\"2025-05-01\",\"type\":\"Specialty Medication\",\"description\":\"Stelara Injection\",\"amount\":675,\"appliedToDeductible\":true}]}}. There is valuable information in the member's profile that you can use to help the user.\n\n[Style]  \n- Maintain a warm, approachable tone, allowing members to feel comfortable and understood.  \n- Use clear, concise language while ensuring empathy and professionalism.  \n\n[Response Guidelines]  \n- Provide concise and direct answers to member inquiries using realistic mock data.  \n- Initiate conversations by greeting the caller warmly. Avoid mentioning the testing context. \n\n[Task & Goals]  \n1. Greet the caller personally and pleasantly as the call begins.\n2. Answer any insurance plan questions using generated mock data, ensuring clarity and relevance to the user's inquiry. Insurance questions should be responded to as if you already have all of their data, eliminating the need to ask them for login or account information.\n3. You can perform all functions asked of you related to the member's profile and insurance plan. You will simulate performing these actions, but will never mention that you are simulating them.\n\n[Error Handling / Fallback]  \n- If a user's input is unclear, ask polite clarifying questions to ensure understanding.  \n- If the conversation veers off-topic, respectfully guide it back to relevant questions about the insurance plan.  \n- Apologize gracefully if a misunderstanding occurs and offer additional assistance. ",
            "role": "system"
          }
        ],
        "tools": [
          {
            "type": "function",
            "function": {
              "name": "scheduleAppointment",
              "description": "Schedule a new appointment for the user and add it to their focus section",
              "parameters": {
                "type": "object",
                "properties": {
                  "type": {
                    "description": "Type of appointment",
                    "type": "string",
                    "enum": [
                      "telehealth",
                      "in-person",
                      "nurseline"
                    ]
                  },
                  "provider": {
                    "description": "Name of the healthcare provider",
                    "type": "string"
                  },
                  "specialty": {
                    "description": "Medical specialty (e.g., Cardiology, Dermatology, General Practice)",
                    "type": "string"
                  },
                  "date": {
                    "description": "Appointment date in YYYY-MM-DD format",
                    "type": "string"
                  },
                  "time": {
                    "description": "Appointment time in HH:MM format (24-hour)",
                    "type": "string"
                  }
                },
                "required": [
                  "type",
                  "provider",
                  "specialty",
                  "date",
                  "time"
                ]
              }
            },
            "server": {
              "url": "https://gennext.vercel.app/api/vapi/schedule-appointment"
            }
          },
          {
            "type": "function",
            "function": {
              "name": "sendChatMessage",
              "description": "Send a message to the user's chat thread",
              "parameters": {
                "type": "object",
                "properties": {
                  "message": {
                    "description": "The message to send to chat",
                    "type": "string"
                  },
                  "type": {
                    "description": "Type of message",
                    "type": "string",
                    "enum": [
                      "regular",
                      "summary",
                      "follow-up"
                    ]
                  }
                },
                "required": [
                  "message"
                ]
              }
            },
            "server": {
              "url": "https://gennext.vercel.app/api/vapi/send-message"
            }
          }
        ],
        "provider": "openai"
      },
      "voice": {
        "provider": "11labs",
        "voiceId": "M6N6IdXhi5YNZyZSDe7k"
      },
      "firstMessage": "Hey there. My name is Liz. How can I help you today? I can help you with a previous topic or we can start a new one.",
      "firstMessageMode": "assistant-speaks-first",
      "serverMessages": [
        "end-of-call-report"
      ],
      "metadata": {
        "userId": "user_1752503153836_lx2b4e12c",
        "threadId": "w00h9rrv9"
      },
      "backgroundSound": "office",
      "credentials": [
        {
          "provider": "11labs",
          "apiKey": "sk_004c5ec28fe947fe30210fdd08b974d720b6ba2e34b113e0"
        }
      ],
      "server": {
        "url": "https://gennext.vercel.app/api/vapi/webhook"
      }
    }
  }
}

