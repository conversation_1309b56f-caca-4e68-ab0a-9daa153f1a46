# Healthcare Virtual Assistant

A comprehensive healthcare virtual assistant application built with Next.js 15, featuring multi-provider AI integration, voice capabilities, and personalized healthcare journey management.

## 🏥 Features

### Core Capabilities
- **Multi-Provider AI Chat**: OpenAI GPT, Google Gemini, and Crayon AI integration
- **Voice Assistant**: Vapi.ai integration for voice conversations and transcripts
- **Healthcare Data Management**: Patient profiles, insurance claims, prescriptions, and care teams
- **Interactive Care Canvas**: Visual healthcare journey management
- **Omnichannel Support**: Customer service interface with mock data

### Key Pages
- **Home**: Main chat interface with AI assistant
- **Care Canvas**: Interactive healthcare journey visualization
- **Profile**: Comprehensive patient profile management
- **Journeys**: Healthcare journey tracking and management

## 🛠️ Technology Stack

- **Framework**: Next.js 15 with App Router
- **Language**: TypeScript with strict configuration
- **Styling**: Tailwind CSS v4 with custom healthcare design system
- **UI Components**: Radix UI primitives with shadcn/ui components
- **AI Integration**: OpenAI GPT, Google Gemini, and Crayon AI
- **Voice**: Vapi.ai for voice assistant capabilities
- **Analytics**: PostHog for user tracking
- **Package Manager**: pnpm

## 🚀 Getting Started

### Prerequisites
- Node.js 18+ 
- pnpm (recommended package manager)

### Installation

1. Clone the repository
2. Install dependencies:
```bash
pnpm install
```

3. Set up environment variables (create `.env.local`):
```env
OPENAI_API_KEY=your_openai_key
GOOGLE_AI_API_KEY=your_gemini_key
CRAYON_AI_API_KEY=your_crayon_key
VAPI_API_KEY=your_vapi_key
POSTHOG_KEY=your_posthog_key
```

### Development

Start the development server:
```bash
pnpm dev
```

Open [http://localhost:3000](http://localhost:3000) to view the application.

### Build & Production

```bash
pnpm build  # Build for production
pnpm start  # Start production server
```

### Code Quality

```bash
pnpm lint   # Run ESLint
```

## 📁 Project Structure

```
src/
├── app/                    # Next.js 15 App Router pages
├── components/
│   ├── ui/                # shadcn/ui components
│   ├── chat/              # Chat interface components
│   ├── profile/           # Profile management components
│   └── care-canvas/       # Care journey visualization
├── contexts/
│   └── ChatContext.tsx    # Global chat state management
├── types/
│   └── index.ts          # Healthcare data type definitions
├── hooks/                 # Custom React hooks
└── lib/                   # Utility functions and configurations
```

## 🔧 Key Features

### AI Assistant Integration
- Multi-provider support with function calling capabilities
- Healthcare-specific AI workflows
- Voice conversation integration
- Persistent chat threads with local storage

### Healthcare Data Management
- Patient profiles with medical conditions
- Insurance plan and claims tracking
- Prescription management with pharmacy integration
- Care team coordination and provider search

### Voice Assistant
- Vapi.ai integration for voice interactions
- Voice call event tracking
- Transcript selection and integration with chat

### Responsive Design
- Mobile-first responsive design
- Custom hooks for responsive behavior
- Tailwind CSS with healthcare-themed design system

## 🎨 Design System

The application uses a custom healthcare-themed design system built on:
- **Tailwind CSS v4** with custom CSS variables
- **Radix UI primitives** for accessibility
- **shadcn/ui components** with "new-york" style
- **Lucide icons** for consistent iconography
- **Dark mode support** with system preference detection

## 🔒 Security & Privacy

- Environment-based configuration for API keys
- Secure handling of healthcare data
- Error tracking with PostHog
- Toast notifications for user feedback

## 📝 Development Notes

- Uses `@/` prefix for all imports (configured in `tsconfig.json`)
- TypeScript strict mode enabled
- ESLint configuration for code quality
- Custom hooks for complex logic patterns
- Functional components with consistent prop interfaces

## 🚀 Deployment

The application is optimized for deployment on Vercel:

[![Deploy with Vercel](https://vercel.com/button)](https://vercel.com/new/clone?repository-url=https://github.com/your-username/gennext)

For other deployment options, refer to the [Next.js deployment documentation](https://nextjs.org/docs/app/building-your-application/deploying).

## 📖 Learn More

- [Next.js Documentation](https://nextjs.org/docs) - Next.js features and API
- [Tailwind CSS](https://tailwindcss.com/docs) - Utility-first CSS framework
- [Radix UI](https://www.radix-ui.com/) - Accessible component primitives
- [OpenAI API](https://platform.openai.com/docs) - AI integration
- [Vapi.ai](https://vapi.ai/) - Voice assistant platform