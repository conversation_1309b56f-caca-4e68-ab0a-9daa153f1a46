# LLM Hooks Documentation

This document provides a comprehensive overview of all hooks and components that send messages to the LLM, including their usage locations, the messages they send, and the UI button text that triggers them.

## Overview

The application uses several patterns to send messages to the LLM:
1. **Direct message sending** through the `addMessage` function from `ChatContext`
2. **Function handlers** that trigger predefined messages
3. **Voice assistant integration** that manages conversation context
4. **Suggestion systems** that send user-selected text

## Core Chat System Functions (ChatContext.tsx)

### Primary Message Handling

#### `addMessage(content, sender)`
- **Location**: `src/contexts/ChatContext.tsx:159`
- **Purpose**: Core function to add messages to the chat thread
- **Usage**: Called by all other components to send user/AI messages
- **Message Types**: User text, AI structured responses (ChatResponsePart[])

#### `sendMessageToAI(messages)`
- **Location**: `src/components/chat/ChatView.tsx:151`
- **Purpose**: Sends formatted messages to the Gemini AI assistant
- **API Endpoint**: `/api/gemini-assistant`
- **Additional Data**: Includes selected transcript content

## Message-Sending Hooks and Handlers

### 1. General Message Input

#### `handleSendMessage()`
- **Location**: `src/components/chat/ChatView.tsx:221`
- **Trigger**: 
  - Send button click (UI button with ArrowUp icon)
  - Enter key press in textarea
  - Form submission
- **UI Button Text**: No text (icon only - ArrowUp)
- **Message Content**: Whatever user types in the textarea
- **Code Reference**: `ChatView.tsx:717-733`

#### `handleSuggestionClick(suggestionText)`
- **Location**: `src/components/chat/ChatView.tsx:235`
- **Trigger**: Clicking on suggestion buttons
- **UI Button Text**: Dynamic text from suggestion object (e.g., "What are some ways you can help me manage my medications?")
- **Message Content**: Exact suggestion text
- **Used By**: ChatSuggestions component buttons

### 2. Specific Action Handlers

#### `handleTimeSelection(selectedTime)`
- **Location**: `src/components/chat/ChatView.tsx:271`
- **Trigger**: Time picker button clicks
- **UI Button Text**: Time slots ("10:00 am", "10:30 am", "11:00 am")
- **Message Content**: `"Selected time: ${selectedTime}"`
- **Component**: `TelehealthAppointmentTimePicker.tsx:13-30`

#### `handleAppointmentJoined()`
- **Location**: `src/components/chat/ChatView.tsx:283`
- **Trigger**: "Join Now" button in telehealth appointment card
- **UI Button Text**: "Join Now"
- **Message Content**: `"Member has joined the telehealth appointment. ...Fast Foward"`
- **Component**: `TelehealthAppointmentCard.tsx:52-57`

#### `handleScheduleCallBack()`
- **Location**: `src/components/chat/ChatView.tsx:295`
- **Trigger**: "Connect with a nurse" button in condition management
- **UI Button Text**: "Connect with a nurse"
- **Message Content**: `"I'd like to schedule a call back with my dedicated nurse."`
- **Component**: `ConditionManagementCard.tsx:32-35`

#### `handleScheduleTelehealth()`
- **Location**: `src/components/chat/ChatView.tsx:307`
- **Trigger**: "Schedule a virtual visit" button in condition management
- **UI Button Text**: "Schedule a virtual visit"
- **Message Content**: `"I'd like to schedule a Telehealth appointment with a specialist"`
- **Component**: `ConditionManagementCard.tsx:43-45`

#### `handleMedicationHelp()`
- **Location**: `src/components/chat/ChatView.tsx:319`
- **Trigger**: "Check refills and reminders" button in condition management
- **UI Button Text**: "Check refills and reminders"
- **Message Content**: `"What are some ways you can help me manage my medications?"`
- **Component**: `ConditionManagementCard.tsx:54-56`

### 3. Welcome Page Handlers

#### `handleSubmit()` (WelcomeTextArea)
- **Location**: `src/components/chat/WelcomeTextArea.tsx:42`
- **Trigger**: 
  - Send button click (ArrowUp icon)
  - Enter key press in textarea
  - Form submission
- **UI Button Text**: No text (icon only - ArrowUp)
- **Message Content**: Whatever user types in the welcome textarea
- **Special Behavior**: Creates new thread and navigates to `/journeys`
- **Code Reference**: `WelcomeTextArea.tsx:136-154`

#### `handleSuggestionClick()` (WelcomeTextArea)
- **Location**: `src/components/chat/WelcomeTextArea.tsx:58`
- **Trigger**: Clicking suggestion buttons on welcome page
- **UI Button Text**: Dynamic suggestion text
- **Message Content**: Exact suggestion text
- **Special Behavior**: Creates new thread and navigates to `/journeys`

### 4. Voice Assistant Integration

#### Voice Call Context Handling
- **Location**: `src/components/chat/VoiceAssistantModal.tsx:31`
- **Trigger**: Voice Assistant button
- **UI Button Text**: "Voice Assistant" / "Voice Active"
- **Message Handling**: 
  - Sends formatted conversation history to Vapi
  - System prompt includes member profile and conversation context
  - Function calls for appointment scheduling
- **Special Features**:
  - Adds voice call start/end events to chat
  - Handles appointment scheduling via voice commands
  - Manages conversation context transfer

## Static Suggestions System

### Initial Static Suggestions
- **Location**: `src/app/data/Static_Suggestions.ts` (imported in ChatView and WelcomeTextArea)
- **Usage**: Displayed when chat thread is empty or on welcome page
- **Examples**:
  - "What are some ways you can help me manage my medications?"
  - "I'd like to schedule a Telehealth appointment with a specialist"
  - "How can I check my claims?"

### Dynamic Suggestions
- **Source**: AI-generated suggestions via `show_suggestions` function calls
- **Display**: Rendered below AI messages
- **Trigger**: Clicking dynamic suggestion buttons
- **Message Content**: Exact suggestion text

## Component-Specific Message Triggers

### ConditionManagementCard.tsx
Contains 4 different action buttons that send messages:

1. **Care Management Button**
   - **Button Text**: "Connect with a nurse"
   - **Message**: `"I'd like to schedule a call back with my dedicated nurse."`
   - **Handler**: `onScheduleCallBack` prop

2. **Virtual Visits Button**
   - **Button Text**: "Schedule a virtual visit"
   - **Message**: `"I'd like to schedule a Telehealth appointment with a specialist"`
   - **Handler**: `onScheduleTelehealth` prop

3. **Medication Management Button**
   - **Button Text**: "Check refills and reminders"
   - **Message**: `"What are some ways you can help me manage my medications?"`
   - **Handler**: `onMedicationHelp` prop

4. **Care Canvas Button**
   - **Button Text**: "Open care canvas"
   - **Message**: None (opens care canvas without sending message)
   - **Handler**: `onOpenCareCanvas` prop

### TelehealthAppointmentTimePicker.tsx
Contains 3 time selection buttons:
- **Button Text**: "10:00 am", "10:30 am", "11:00 am"
- **Message**: `"Selected time: ${selectedTime}"`
- **Handler**: `onTimeSelect` prop

### TelehealthAppointmentCard.tsx
- **Button Text**: "Join Now"
- **Message**: `"Member has joined the telehealth appointment. ...Fast Foward"`
- **Handler**: `onJoinNow` prop

### ChatSuggestions.tsx
- **Button Text**: Dynamic (suggestion.text)
- **Message**: Exact suggestion text
- **Handler**: `onSuggestionClick` prop
- **Styling**: Uses RippleButton with outline variant

## System Message Handlers

### Voice Call Events
- **addVoiceCallEvent(eventType, targetThreadId)**
- **Location**: `src/contexts/ChatContext.tsx:182`
- **Message Types**: 
  - `'Voice call started'` (system message)
  - `'Voice call ended'` (system message)
- **Special Behavior**: These are system messages that don't get sent to the AI

## API Integration

### Primary AI Endpoint
- **Endpoint**: `/api/gemini-assistant`
- **Method**: POST
- **Payload**: 
  - `messages`: Formatted conversation history
  - `transcripts`: Selected transcript content (optional)
- **Response**: Array of `ChatResponsePart` objects

### Message Formatting
- **Function**: `formatMessagesForApi(messages)`
- **Location**: `src/components/chat/ChatView.tsx:116`
- **Purpose**: Converts internal message format to API format
- **Excludes**: System messages (voice call events)

## For You Items Management

### Dynamic Content Addition
- **Function**: `addForYouItems(items)`
- **Trigger**: AI response with `enable_condition_management_features` function result
- **Purpose**: Adds new personalized suggestions to user's available options
- **Storage**: localStorage with key `'allAvailableForYouItems'`

## Transcript Integration

### Transcript Attachment
- **UI Element**: "Attach Transcripts" button
- **Location**: ChatView input area
- **Functionality**: Opens transcript selection sheet
- **Message Enhancement**: Selected transcripts are included in API calls
- **Auto-clear**: Transcripts are cleared after sending message

## Notes for Consolidation

1. **Message Sending Patterns**: All message sending ultimately flows through `addMessage()` in ChatContext
2. **Handler Consistency**: Most handlers follow the pattern of adding user message, then calling `sendMessageToAI()`
3. **UI Button Patterns**: Mix of text buttons, icon buttons, and suggestion chips
4. **Context Preservation**: Voice assistant maintains conversation context across modalities
5. **Function Call Integration**: Some components trigger both messages and function calls (e.g., voice appointments)
6. **Navigation Integration**: Welcome page handlers also trigger navigation to `/journeys`

## Potential Consolidation Areas

1. **Standardize Handler Patterns**: All action handlers could use a common interface
2. **Centralize Message Templates**: Predefined messages could be moved to a constants file
3. **Unify Button Styling**: Consistent button styling across components
4. **Common Hook**: Create a `useLLMMessage()` hook for consistent message sending
5. **Voice Integration**: Standardize how voice and text interactions are combined