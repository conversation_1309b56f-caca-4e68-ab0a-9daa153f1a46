# Project Tasks

## 1. Sidebar Trigger Bug Fix
**Status:** ✅ COMPLETED - Agent: sidebar-trigger-fix
**Commit:** 190e383 - fix: prevent sidebar from getting stuck open on journeys page

**User Description:** "close sidebar trigger doesn't seem to work while on the journeys page when navigating from internal login page or internal crohns diagnosis page. We implemented a change to make sure the sidebar is closed and the trigger is hidden on those internal pages, and also implemented a change to make sure the sidebar is open on the journeys page, but that journeys page change was only suppose to ensure it was open on load. not stuck open. Have a look, identify to bug/issue, implement a solution."

**Task:** Investigate the sidebar trigger functionality on the journeys page when navigating from internal pages. The sidebar should be able to close properly after opening on load.

## 2. Internal Page Styling Improvements
**Status:** ✅ COMPLETED - Agent: internal-page-styling
**Commit:** ff6f0ca - feat: redesign internal page with professional centered layout

**User Description:** "internal/page.tsx needs better styling. it doesn't look professional the way things laidout on the page. everything should be centered on the page."

**Task:** Improve the styling and layout of internal/page.tsx to create a more professional appearance with centered content.

## 3. Nurse Line References Audit
**Status:** ✅ COMPLETED - Agent: nurse-line-audit
**Commit:** 49da345 - docs: add comprehensive nurse line audit documentation

**User Description:** "create a new md file and list out every where a dedicated nurse line or nurse line or nurse call is mentioned. We'll need to refactor what we call that soon and it's helpful to determine every instance where it's used."

**Task:** Create a comprehensive MD file documenting all instances where "dedicated nurse line", "nurse line", or "nurse call" are mentioned throughout the codebase.

## 4. LLM Message Hooks Documentation
**Status:** ✅ COMPLETED - Agent: llm-hooks-docs
**Commit:** da6a206 - docs: add comprehensive LLM hooks documentation

**User Description:** "create a new md file and list out everywhere we use a hook that sends a message to the llm. I think all the hooks are in chatcontext but they are used in various places. Some components in the /chat folder send messages when buttons are pressed, idk if those use the same hooks. We'll soon need to create some consolidation in verbagie with ones that are very similar, so what message is sent and what the ui button says is very important information in your writeup."

**Task:** Document all hooks that send messages to the LLM, including their usage locations, the messages they send, and the UI button text that triggers them.

## 5. Chat Threads Management Investigation
**Status:** ✅ COMPLETED - Agent: chat-threads-investigation
**Commit:** 04ce016 - Add comprehensive analysis of Vapi voice assistant integration

**User Description:** "I need help identifying and understanding how we are managing chatthreads. reason: we are going to start to do more with vapi voice assistant. transcripts and sending messages back to the ui in the chatview are going to be important features moving forward and we'll need a way to make sure the transcripts and message make their way into the thread where the voice assistant session was started. right now we are using the vapi web sdk in chat threads which helps with this with their events but soon we'll we using the outbound call feature in chats. After vapi sessions they do send a payload which can be configured to be send to an endpoint. what I've done in the past with another project is have that payload go to an api route, and then have my api route send desired data from the payload to upstash redis and then have our app poll(I think that's the right word) to pull in that data to display on the frontend based on user session and chat thread is. feels like a lot of steps. investigate that approach but also determine if there are simpler alternatives."

**Task:** Analyze current chat thread management system and investigate integration options for vapi voice assistant transcripts. Evaluate the current Redis polling approach and explore simpler alternatives for handling vapi payloads and transcript integration.