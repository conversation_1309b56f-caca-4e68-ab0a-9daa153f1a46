# Nurse Line Audit Documentation

This document catalogs all instances where 'dedicated nurse line', 'nurse line', or 'nurse call' are mentioned throughout the codebase as of July 12, 2025.

## Overview

The nurse line functionality appears to be a key feature across the healthcare platform, providing members with direct access to nursing support through various touchpoints including the Care Canvas, outbound calling systems, and AI assistant interactions.

## File Locations and Context

### 1. `/src/hooks/use-care-canvas-actions.ts`

**Lines 127-131**: `handleDedicatedNurseLine` function
```typescript
const handleDedicatedNurseLine = useCallback(async () => {
  const nurseMessage = "I'd like to schedule a call back with dedicated nurse line";
  const threadName = "Dedicated Nurse Line";
  await handleChatRequest(nurseMessage, threadName);
}, [handleChatRequest]);
```

**Lines 157-161**: `handleNurselineReschedule` function
```typescript
const handleNurselineReschedule = useCallback(async () => {
  const rescheduleMessage = "I'd like to reschedule my nurse line call";
  const threadName = "Reschedule Nurse Line";
  await handleChatRequest(rescheduleMessage, threadName);
}, [handleChatRequest]);
```

**Context**: This hook provides action handlers for Care Canvas interactions, specifically for scheduling and rescheduling nurse line calls. These functions create chat threads that integrate with the AI assistant system.

### 2. `/src/components/care-canvas/CareCanvas.tsx`

**Line 162**: Type checking for nurse line appointments
```typescript
appointment.type === 'nurseline' ? 'Nurse Line Call' : 'In-Person'} Appointment
```

**Lines 183-192**: Nurse line appointment UI with reschedule functionality
```typescript
) : appointment.type === 'nurseline' ? (
  <Button
    size="sm"
    variant="outline"
    onClick={() => handleNurselineReschedule()}
    className="flex-1 text-xs border-slate-300 dark:border-slate-600 hover:bg-slate-50 dark:hover:bg-slate-700/50"
  >
    <Phone className="h-3 w-3 mr-1" />
    Reschedule
  </Button>
```

**Lines 581-594**: Dedicated Nurse Line section in Condition Management
```typescript
<h3 className="font-medium text-sm flex items-center gap-2">
  <Phone className="h-4 w-4 text-purple-600 dark:text-purple-400" />
  Dedicated Nurse Line
</h3>
<p className="text-xs text-gray-600 dark:text-slate-300 mt-1">
  Connect with a dedicated nurse for personalized support
</p>
<Button
  size="sm"
  variant="outline"
  onClick={() => handleDedicatedNurseLine()}
  className="mt-2 text-xs border-purple-300 dark:border-purple-500 hover:bg-purple-50 dark:hover:bg-purple-500/10 text-purple-700 dark:text-purple-300"
>
  Schedule Call Back
</Button>
```

**Context**: The CareCanvas component displays nurse line functionality in two contexts: as schedulable appointments in the Focus section and as a dedicated service in the Condition Management section.

### 3. `/src/app/internal/clinical-outreach/page.tsx`

**Line 165**: Reference to 24/7 Nurse Line in clinical outreach benefits
```typescript
<li>• Provide information about 24/7 Nurse Line availability</li>
```

**Context**: This appears in the clinical outreach experience description, highlighting the nurse line as a post-surgical care benefit available to members.

### 4. `/src/app/care-canvas/page.tsx`

This file contains the same nurse line references as the CareCanvas component since it imports and uses the CareCanvas component directly. The references are functionally identical to those found in `/src/components/care-canvas/CareCanvas.tsx`.

### 5. `/src/app/api/outbound-call/route.ts`

**Line 68**: Nurse Line mentioned in system prompt for AI assistant
```typescript
- Inform them about the 24/7 Nurse Line available for any concerns or questions
```

**Context**: Part of the AI assistant configuration for post-surgical follow-up calls, where the virtual assistant (Liz) informs patients about available nurse line services.

### 6. `/src/app/api/gemini-assistant/route.ts`

**Lines 649-657**: Feature distinction documentation
```typescript
## Dedicated Nurse Line vs. Personalized Condition Management

**These are TWO SEPARATE features:**

1. **Dedicated Nurse Line**: Standalone nurse support available to all members

2. **Personalized Condition Management**: Comprehensive condition management health plan feature that includes an enhanced Dedicated Nurse Line

**NEVER conflate these features.**
```

**Lines 669-717**: Comprehensive Dedicated Nurse Line workflow
```typescript
## Dedicated Nurse Line
When a user requests to connect with a dedicated nurse follow this comprehensive flow:

### Step 1: Proactive Offer and Explanation
- **Trigger:** User asks about nursing support OR mentions a condition from their profile
- **Action:** Explain the Dedicated Nurse Line service and its benefits
- **Your Response (use `send_text_response`):**
    - **`answer`:** "I can connect you with our Dedicated Nurse Line, which provides personalized support from a registered nurse who specializes in [condition if applicable]. Your dedicated nurse can help with symptom management, medication questions, care coordination, and provide guidance between doctor visits. They're available to support you through your healthcare journey."
```

**Lines 298-299**: Function parameter for nurse line appointment type
```typescript
enum: ['telehealth', 'in-person', 'nurseline'],
description: 'The type of appointment: "telehealth", "in-person", or "nurseline".'
```

**Lines 303-304**: Provider description for nurse line services
```typescript
description: 'The name of the healthcare provider or service, e.g., "Dr. Sarah Mitchell" or "Dedicated Nurse Line".'
```

**Lines 699-701**: Function call configuration for nurse line appointments
```typescript
- **`type`:** "nurseline"
- **`provider`:** "Dedicated Nurse Line"
- **`specialty`:** "Nurse Support"
```

**Context**: The Gemini assistant API route contains extensive documentation and workflows for nurse line functionality, including detailed conversation flows, function calling patterns, and feature distinctions.

### 7. `/Care_Canvas_Augment_Previous_Thread.md`

**Line 128**: Documentation reference to Dedicated Nurse Line
```typescript
- **Dedicated Nurse Line**: Purple theme, "Schedule Call Back" button
```

**Context**: This appears to be design/implementation documentation describing the UI theming and functionality for the nurse line feature within the Care Canvas component.

## Summary by Feature Type

### Core Functionality
- **Scheduling**: Users can schedule initial nurse line calls via Care Canvas
- **Rescheduling**: Users can reschedule existing nurse line appointments
- **24/7 Availability**: Nurse line is promoted as available around the clock
- **Chat Integration**: All nurse line interactions create chat threads with the AI assistant

### UI Components
- **Focus Section**: Displays scheduled nurse line calls with reschedule functionality
- **Condition Management**: Provides dedicated nurse line access with "Schedule Call Back" button
- **Clinical Outreach**: Promotes nurse line as a post-care benefit

### AI Integration
- **Function Calling**: `add_appointment_to_focus` supports 'nurseline' type appointments
- **Conversation Flows**: Detailed workflows for nurse line scheduling and support
- **Feature Distinction**: Clear separation between standalone nurse line and condition management programs

## Technical Implementation Notes

1. **Appointment Types**: The system recognizes 'nurseline' as a distinct appointment type alongside 'telehealth' and 'in-person'
2. **Data Storage**: Nurse line appointments are stored in localStorage via the focus appointments system
3. **Chat Threading**: All nurse line interactions create dedicated chat threads for continued conversation
4. **AI Workflows**: Comprehensive conversation flows ensure consistent user experience across all nurse line interactions

## Architectural Patterns

The nurse line functionality follows these key patterns:
- **Chat-First Approach**: Primary interactions route through the chat/AI system
- **Component Integration**: Nurse line features are embedded within larger healthcare management components
- **Service Distinction**: Clear architectural separation between different types of nursing support services
- **Progressive Enhancement**: UI provides immediate access while AI handles complex scheduling and coordination

This audit reveals that nurse line functionality is deeply integrated throughout the platform, serving as both a standalone service and an enhanced feature within condition management programs.