# 🐳 Container Development Workflow

## Quick Start

### VS Code Method (Recommended)
1. Open project in VS Code
2. `Ctrl+Shift+P` → "Dev Containers: Reopen in Container"
3. Wait for container to build and start
4. Start coding! 🚀

### Terminal-Only Method
```bash
# Build the container
docker build -t gennext-dev .devcontainer

# Run the container
docker run -it --rm \
  -v $(pwd):/workspace \
  -v ~/.claude:/home/<USER>/.claude \
  -p 3000:3000 \
  --name gennext-dev \
  gennext-dev

# Or use docker-compose (if you prefer)
docker-compose -f .devcontainer/docker-compose.yml up -d
```

## 📁 File Synchronization

**✅ Real-time sync** - No manual copying needed!
- Edit files in container → Changes appear locally instantly
- Git operations work normally
- Your local files are bind-mounted to `/workspace`

## 🔧 Terminal Tools Available

### Session Management
- **tmux**: `tmux new-session -s dev`
- **zellij**: `zellij` (modern alternative to tmux)

### Development Commands
```bash
# Install dependencies
pnpm install

# Start dev server
pnpm dev

# Build for production
pnpm build

# Run linting
pnpm lint
```

## 🚀 Claude Code Integration

### Authentication
- Claude Code auth is automatically shared via bind mount
- No login required inside container
- Use `claude --dangerously-skip-permissions` for safer execution

### Usage
```bash
# Claude Code commands work normally
claude chat
claude diff
claude edit
```

## 🛠️ Terminal Access Methods

### From VS Code
- `Ctrl+` ` - Opens integrated terminal (already in container)

### From External Terminal
```bash
# Find running container
docker ps

# Access container
docker exec -it <container_name> bash

# With tmux
docker exec -it <container_name> tmux

# With zellij
docker exec -it <container_name> zellij
```

## 🔒 Security Features

- **Isolated environment** with custom firewall rules
- **Network restrictions** to essential services only
- **Non-root user** for safer operations
- **Port 3000** automatically forwarded for dev server

## 🎯 Common Workflows

### Daily Development
1. Start container (VS Code or terminal)
2. Open tmux/zellij session
3. Run `pnpm dev` in one pane
4. Code in another pane
5. Use Claude Code for assistance
6. Commit changes (git works normally)

### Debugging
1. Access container terminal
2. Run diagnostic commands
3. Check logs: `docker logs <container_name>`
4. Files are instantly available locally for inspection

## 📋 Troubleshooting

### Container Won't Start
```bash
# Rebuild container
docker build --no-cache -t gennext-dev .devcontainer

# Check logs
docker logs <container_name>
```

### Port Already in Use
```bash
# Stop existing containers
docker stop $(docker ps -q)

# Or change port mapping
docker run -p 3001:3000 ...
```

### Files Not Syncing
- Check bind mount: `-v $(pwd):/workspace`
- Ensure you're in the project directory
- Restart container if needed

## 🎨 Customization

### Add More Tools
Edit `.devcontainer/Dockerfile`:
```dockerfile
RUN apt-get update && apt-get install -y \
    your-tool-here \
    && rm -rf /var/lib/apt/lists/*
```

### Change VS Code Extensions
Edit `.devcontainer/devcontainer.json`:
```json
"extensions": [
    "your.extension.id"
]
```

---

**💡 Pro Tip**: Use tmux/zellij to maintain persistent sessions even when disconnecting from the container!