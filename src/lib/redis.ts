import { Redis } from '@upstash/redis'
import { getUserId, generateId } from './utils'

// Initialize Redis client with error handling for missing credentials
function createRedisClient() {
  const url = process.env.UPSTASH_REDIS_REST_URL;
  const token = process.env.UPSTASH_REDIS_REST_TOKEN;

  // Check if we have valid credentials
  if (!url || !token || url.includes('your_upstash_redis_url_here') || token.includes('your_upstash_redis_token_here')) {
    console.warn('Redis credentials not configured. Redis functionality will be disabled.');
    return null;
  }

  try {
    return Redis.fromEnv();
  } catch (error) {
    console.error('Failed to initialize Redis client:', error);
    return null;
  }
}

export const redis = createRedisClient();

// Helper function to generate unique IDs
export function generateItemId(type: string): string {
  return `${type}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
}

// Get current user ID (now uses real implementation)
export function getCurrentUserId(): string {
  return getUserId()
}

// Helper function to check if Redis is available
export function isRedisAvailable(): boolean {
  return redis !== null;
}
