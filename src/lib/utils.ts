import { type ClassValue, clsx } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

export function generateId(): string {
  return Math.random().toString(36).substr(2, 9);
}

export function getCurrentTimestamp(): number {
  return Date.now();
}

// NEW: User ID management functions
const USER_ID_KEY = 'app_user_id';

export function generateUserId(): string {
  return `user_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}

export function getUserId(): string {
  // Check if we're in browser environment
  if (typeof window === 'undefined') {
    return 'user_server'; // Fallback for server-side
  }

  try {
    // Get existing user ID from localStorage
    let userId = localStorage.getItem(USER_ID_KEY);

    if (!userId) {
      // Generate new user ID on first visit
      userId = generateUserId();
      localStorage.setItem(USER_ID_KEY, userId);
      console.log('Generated new user ID:', userId);
    }

    return userId;
  } catch (error) {
    console.error('Error managing user ID:', error);
    return `user_fallback_${Date.now()}`;
  }
}

export function resetUserId(): string {
  // For testing/demo purposes - generate new user ID
  const newUserId = generateUserId();
  try {
    localStorage.setItem(USER_ID_KEY, newUserId);
    console.log('Reset to new user ID:', newUserId);
  } catch (error) {
    console.error('Error resetting user ID:', error);
  }
  return newUserId;
}
