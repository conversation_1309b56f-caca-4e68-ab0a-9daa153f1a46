import type { <PERSON>ada<PERSON> } from "next";
import { Inter } from "next/font/google";
import "./globals.css";
import { ChatProvider } from "@/contexts/ChatContext";
import { Toaster } from "@/components/ui/sonner"; // Assuming sonner is in ui components
import { ThemeProvider } from "@/components/ThemeProvider";
import { SidebarProvider } from "@/components/ui/sidebar"
import { AppSidebar } from "@/components/chat/Sidebar"
import { CustomSidebarTrigger } from "@/components/ui/CustomSidebarTrigger";
//import { StagewiseToolbar } from '@stagewise/toolbar-next';
import { PostHogProvider } from "@/app/posthog/posthog-provider";


const inter = Inter({ subsets: ["latin"] });
const stagewiseConfig = {
  plugins: []
};

export const metadata: Metadata = {
  title: "Virtual Assistant",
  description: "The Future of Health Insurance",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body className={inter.className}>
        <ThemeProvider
          attribute="class"
          defaultTheme="dark"
          disableTransitionOnChange
        >
          <PostHogProvider>
              <ChatProvider>
               <SidebarProvider>
                <AppSidebar/>
                <CustomSidebarTrigger />
                <main className="flex-grow w-full overflow-auto bg-genui">
                  
                {children}
                <Toaster />
                {/* {process.env.NODE_ENV === 'development' && (
                  <StagewiseToolbar config={stagewiseConfig} />
                )} */}
                 </main>
                 </SidebarProvider>
              </ChatProvider>
              </PostHogProvider>
          
        </ThemeProvider>
      </body>
    </html>
  );
}
