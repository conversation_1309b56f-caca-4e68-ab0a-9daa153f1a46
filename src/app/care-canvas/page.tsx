'use client';

import React, { useState, useRef } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import { Textarea } from '@/components/ui/textarea';
import {
  ChevronDown,
  Pill,
  RefreshCw,
  User,
  Calendar,
  AlertCircle,
  Stethoscope,
  Phone,
  Focus,
  FileText,
  HeartHandshake,
  Activity,
  Shield,
  Video,
  Navigation,
  DollarSign,
  TrendingUp,
  ArrowUp,
  Loader2
} from 'lucide-react';
import { useProfile } from '@/hooks/use-profile';
import { useCareCanvasActions } from '@/hooks/use-care-canvas-actions';
import { useFocusAppointments } from '@/hooks/use-focus-appointments';
import { useChat } from '@/contexts/ChatContext';
import { useIsMobile } from '@/hooks/use-mobile';
import { useRouter } from 'next/navigation';
import { Prescription, CareTeamMember, Claim } from '@/types';
import { cn } from '@/lib/utils';
import { useSidebar } from '@/components/ui/sidebar';

export default function CareCanvasPage() {
  const { profile, isLoading, error } = useProfile();
  const {
    handleRefillRequest,
    handleAppointmentRequest,
    handleClaimInquiry,
    handleFindCare,
    handleDedicatedNurseLine,
    handleConditionTelehealth,
    handleTelehealthJoin,
    handleMedicationQuestion,
    handleMedicationReminders
  } = useCareCanvasActions();
  const { appointments } = useFocusAppointments();
  const { createThread, threads } = useChat();
  const isMobile = useIsMobile();
  const router = useRouter();
  const { open } = useSidebar();

  // Chat input state
  const [chatMessage, setChatMessage] = useState('');
  const [isSending, setIsSending] = useState(false);
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  const sidebarWidth = 'var(--sidebar-width, 190px)';
  const inputViewLeft = !isMobile && open ? `left-[var(--sidebar-width)]` : 'left-0';

  // All collapsibles open by default for full page layout
  const [isFocusOpen, setIsFocusOpen] = useState(true);
  const [isCareTeamOpen, setIsCareTeamOpen] = useState(true);
  const [isRxOpen, setIsRxOpen] = useState(true);
  const [isClaimsOpen, setIsClaimsOpen] = useState(true);
  const [isConditionMgmtOpen, setIsConditionMgmtOpen] = useState(true);
  const [isPlanUsageOpen, setIsPlanUsageOpen] = useState(true);
  const [isWellnessOpen, setIsWellnessOpen] = useState(true);

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-slate-900">
        <div className="container mx-auto px-4 py-8">
          <div className="animate-pulse space-y-6">
            <div className="h-8 bg-gray-200 dark:bg-slate-700 rounded w-1/3 mx-auto"></div>
            <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
              {[...Array(6)].map((_, i) => (
                <div key={i} className="h-64 bg-gray-200 dark:bg-slate-700 rounded-lg"></div>
              ))}
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (error || !profile) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-slate-900 flex items-center justify-center">
        <div className="text-center text-red-500">
          <AlertCircle className="h-12 w-12 mx-auto mb-4" />
          <h2 className="text-xl font-semibold mb-2">Failed to load profile data</h2>
          <p className="text-gray-600 dark:text-gray-400">Please try refreshing the page</p>
        </div>
      </div>
    );
  }

  // Filter out supplements and Ethan's prescriptions, get only prescriptions with refill data
  const prescriptions = profile.prescriptions.filter((prescription: Prescription) => {
    const isNotSupplement = !prescription.medicationName.toLowerCase().includes('supplement') &&
                           !prescription.medicationName.toLowerCase().includes('vitamin') &&
                           !prescription.medicationName.toLowerCase().includes('calcium');
    const isNotEthan = prescription.memberName !== 'Ethan Chen';
    return isNotSupplement && isNotEthan;
  }).slice(0, 3); // Limit to 3 for better UX

  // Check if prescription is eligible for refill
  const isEligibleForRefill = (prescription: Prescription): boolean => {
    return (prescription.refillsRemaining ?? 0) > 0 &&
           prescription.lastFillDate !== undefined;
  };

  const handleRefillClick = (prescription: Prescription) => {
    const medicationName = prescription.brandName || prescription.medicationName;
    handleRefillRequest(medicationName);
  };

  // Get recent claims (limit to 3 for display)
  const recentClaims = profile.claims?.slice(0, 3) || [];

  // Chat input handlers
  const handleChatSubmit = (event?: React.FormEvent<HTMLFormElement>) => {
    if (event) {
      event.preventDefault();
    }
    if (chatMessage.trim()) {
      setIsSending(true);

      createThread({ name: `Care Canvas Chat ${threads.length + 1}`, initialMessage: chatMessage });
      router.push('/journeys');
      setChatMessage('');
      setIsSending(false);
      textareaRef.current?.focus();
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 dark:from-slate-900 dark:to-slate-800 pb-20">
      <div className="container mx-auto px-4 py-8 pb-32">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100 mb-2">
            Virtual Assistant
          </h1>
          <p className="text-lg text-gray-600 dark:text-gray-400">
            Your personalized healthcare journey starts here.
          </p>
        </div>

        {/* Grid Layout */}
        <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
          
          {/* Focus Section */}
          <Card className="shadow-lg border-0 bg-white/80 dark:bg-slate-800/80 backdrop-blur-sm">
            <Collapsible open={isFocusOpen} onOpenChange={setIsFocusOpen}>
              <CollapsibleTrigger asChild>
                <CardHeader className="cursor-pointer hover:bg-gray-50 dark:hover:bg-slate-700/50 transition-colors rounded-t-lg">
                  <CardTitle className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <div className="p-2 rounded-lg bg-blue-100 dark:bg-blue-500/20">
                        <Focus className="h-5 w-5 text-blue-600 dark:text-blue-400" />
                      </div>
                      <div>
                        <span className="text-lg">Focus</span>
                        <Badge variant="secondary" className="ml-2">
                          {appointments.length}
                        </Badge>
                      </div>
                    </div>
                    <ChevronDown className={cn(
                      "h-4 w-4 transition-transform",
                      isFocusOpen ? "rotate-180" : ""
                    )} />
                  </CardTitle>
                </CardHeader>
              </CollapsibleTrigger>
              <CollapsibleContent>
                <CardContent className="pt-4">
                  {appointments.length === 0 ? (
                    <div className="text-center py-8">
                      <Focus className="h-12 w-12 text-gray-300 dark:text-gray-600 mx-auto mb-3" />
                      <p className="text-gray-500 dark:text-gray-400">
                        No items to focus on right now
                      </p>
                    </div>
                  ) : (
                    <div className="space-y-4">
                      {appointments.map((appointment) => (
                        <div
                          key={appointment.id}
                          className="p-4 rounded-xl bg-gradient-to-r from-blue-50 to-blue-100 dark:from-slate-700/50 dark:to-slate-600/50 border border-blue-200 dark:border-blue-500/30 space-y-3"
                        >
                          <div className="flex items-start justify-between">
                            <div className="flex-1">
                              <h3 className="font-semibold text-base mb-1">
                                {appointment.type === 'telehealth' ? 'Telehealth' : 'In-Person'} Appointment
                              </h3>
                              <p className="text-sm text-gray-600 dark:text-slate-300 mb-2">
                                {appointment.provider} • {appointment.specialty}
                              </p>
                              <div className="flex items-center gap-2 text-sm text-gray-500 dark:text-slate-400">
                                <Calendar className="h-4 w-4" />
                                <span>{appointment.date} at {appointment.time}</span>
                              </div>
                            </div>
                          </div>
                          <div className="flex gap-3 pt-2">
                            {appointment.type === 'telehealth' ? (
                              <Button
                                size="sm"
                                onClick={() => handleTelehealthJoin()}
                                className="flex-1 bg-blue-600 hover:bg-blue-700 dark:bg-blue-500 dark:hover:bg-blue-400"
                              >
                                <Video className="h-4 w-4 mr-2" />
                                Join Now
                              </Button>
                            ) : (
                              <Button
                                size="sm"
                                variant="outline"
                                onClick={() => {/* Placeholder */}}
                                className="flex-1 border-slate-300 dark:border-slate-600 hover:bg-slate-50 dark:hover:bg-slate-700/50"
                              >
                                <Navigation className="h-4 w-4 mr-2" />
                                Directions
                              </Button>
                            )}
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </CardContent>
              </CollapsibleContent>
            </Collapsible>
          </Card>

          {/* Care Team Section */}
          <Card className="shadow-lg border-0 bg-white/80 dark:bg-slate-800/80 backdrop-blur-sm">
            <Collapsible open={isCareTeamOpen} onOpenChange={setIsCareTeamOpen}>
              <CollapsibleTrigger asChild>
                <CardHeader className="cursor-pointer hover:bg-gray-50 dark:hover:bg-slate-700/50 transition-colors rounded-t-lg">
                  <CardTitle className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <div className="p-2 rounded-lg bg-emerald-100 dark:bg-emerald-500/20">
                        <Stethoscope className="h-5 w-5 text-emerald-600 dark:text-emerald-400" />
                      </div>
                      <div>
                        <span className="text-lg">Care Team</span>
                        <Badge variant="secondary" className="ml-2">
                          {profile?.careTeam?.length || 0}
                        </Badge>
                      </div>
                    </div>
                    <ChevronDown className={cn(
                      "h-4 w-4 transition-transform",
                      isCareTeamOpen ? "rotate-180" : ""
                    )} />
                  </CardTitle>
                </CardHeader>
              </CollapsibleTrigger>
              <CollapsibleContent>
                <CardContent className="pt-4">
                  {!profile?.careTeam || profile.careTeam.length === 0 ? (
                    <div className="text-center py-8">
                      <Stethoscope className="h-12 w-12 text-gray-300 dark:text-gray-600 mx-auto mb-3" />
                      <p className="text-gray-500 dark:text-gray-400">
                        No care team members found
                      </p>
                    </div>
                  ) : (
                    <div className="space-y-4">
                      {profile.careTeam.map((member) => (
                        <div
                          key={member.providerId}
                          className="p-4 rounded-xl bg-gradient-to-r from-emerald-50 to-emerald-100 dark:from-slate-700/50 dark:to-slate-600/50 border border-emerald-200 dark:border-emerald-500/30 space-y-3"
                        >
                          <div className="flex items-start justify-between">
                            <div className="flex-1">
                              <div className="flex items-center gap-2 mb-2">
                                <h3 className="font-semibold text-base">
                                  {member.name}
                                </h3>
                                {member.isPrimary && (
                                  <Badge variant="outline" className="border-emerald-300 dark:border-emerald-500 text-emerald-700 dark:text-emerald-300">
                                    PCP
                                  </Badge>
                                )}
                              </div>
                              <p className="text-sm text-gray-600 dark:text-slate-300">
                                {member.specialty}
                              </p>
                            </div>
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => handleAppointmentRequest(member.name)}
                              className="border-slate-300 dark:border-slate-600 hover:bg-slate-50 dark:hover:bg-slate-700/50"
                            >
                              Schedule
                            </Button>
                          </div>
                        </div>
                      ))}
                      <div className="mt-6 pt-4 border-t border-gray-200 dark:border-slate-700 space-y-3">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => {/* Placeholder */}}
                          className="w-full text-blue-600 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300 hover:bg-blue-50 dark:hover:bg-blue-500/10"
                        >
                          View Full Care Team
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleFindCare()}
                          className="w-full text-blue-600 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300 hover:bg-blue-50 dark:hover:bg-blue-500/10"
                        >
                          Find Care
                        </Button>
                      </div>
                    </div>
                  )}
                </CardContent>
              </CollapsibleContent>
            </Collapsible>
          </Card>

          {/* Prescriptions Section */}
          <Card className="shadow-lg border-0 bg-white/80 dark:bg-slate-800/80 backdrop-blur-sm">
            <Collapsible open={isRxOpen} onOpenChange={setIsRxOpen}>
              <CollapsibleTrigger asChild>
                <CardHeader className="cursor-pointer hover:bg-gray-50 dark:hover:bg-slate-700/50 transition-colors rounded-t-lg">
                  <CardTitle className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <div className="p-2 rounded-lg bg-indigo-100 dark:bg-indigo-500/20">
                        <Pill className="h-5 w-5 text-indigo-600 dark:text-indigo-400" />
                      </div>
                      <div>
                        <span className="text-lg">Prescriptions</span>
                        <Badge variant="secondary" className="ml-2">
                          {prescriptions.length}
                        </Badge>
                      </div>
                    </div>
                    <ChevronDown className={cn(
                      "h-4 w-4 transition-transform",
                      isRxOpen ? "rotate-180" : ""
                    )} />
                  </CardTitle>
                </CardHeader>
              </CollapsibleTrigger>
              <CollapsibleContent>
                <CardContent className="pt-4">
                  {prescriptions.length === 0 ? (
                    <div className="text-center py-8">
                      <Pill className="h-12 w-12 text-gray-300 dark:text-gray-600 mx-auto mb-3" />
                      <p className="text-gray-500 dark:text-gray-400">
                        No prescriptions found
                      </p>
                    </div>
                  ) : (
                    <div className="space-y-4">
                      {prescriptions.map((prescription) => (
                        <div
                          key={prescription.prescriptionId}
                          className="p-4 rounded-xl bg-gradient-to-r from-indigo-50 to-indigo-100 dark:from-slate-700/50 dark:to-slate-600/50 border border-indigo-200 dark:border-indigo-500/30 space-y-3"
                        >
                          <div className="flex items-start justify-between">
                            <div className="flex-1">
                              <div className="flex items-center gap-2 mb-2">
                                <User className="h-4 w-4 text-gray-500 dark:text-slate-400" />
                                <span className="text-sm text-gray-500 dark:text-slate-400">{prescription.memberName}</span>
                              </div>
                              <h3 className="font-semibold text-base mb-1">
                                {prescription.medicationName}
                                {prescription.brandName && (
                                  <span className="text-gray-600 dark:text-slate-300 ml-1 font-normal">
                                    ({prescription.brandName})
                                  </span>
                                )}
                              </h3>
                              <p className="text-sm text-gray-600 dark:text-slate-300 mb-1">
                                {prescription.frequency}
                              </p>
                              {prescription.refillsRemaining !== undefined && (
                                <p className="text-sm text-gray-500 dark:text-slate-400">
                                  {prescription.refillsRemaining} refills left
                                </p>
                              )}
                            </div>

                            {isEligibleForRefill(prescription) && (
                              <Button
                                size="sm"
                                variant="outline"
                                onClick={() => handleRefillClick(prescription)}
                                className="ml-2 flex items-center gap-2"
                              >
                                <RefreshCw className="h-4 w-4" />
                                Refill
                              </Button>
                            )}
                          </div>
                        </div>
                      ))}
                      <div className="mt-6 pt-4 border-t border-gray-200 dark:border-slate-700 space-y-3">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => {/* Placeholder */}}
                          className="w-full text-blue-600 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300 hover:bg-blue-50 dark:hover:bg-blue-500/10"
                        >
                          View All Prescriptions
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleMedicationQuestion()}
                          className="w-full text-blue-600 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300 hover:bg-blue-50 dark:hover:bg-blue-500/10"
                        >
                          Ask about my medications
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleMedicationReminders()}
                          className="w-full text-blue-600 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300 hover:bg-blue-50 dark:hover:bg-blue-500/10"
                        >
                          Setup Medication Reminders
                        </Button>
                      </div>
                    </div>
                  )}
                </CardContent>
              </CollapsibleContent>
            </Collapsible>
          </Card>

          {/* Plan Usage Section */}
          <Card className="shadow-lg border-0 bg-white/80 dark:bg-slate-800/80 backdrop-blur-sm">
            <Collapsible open={isPlanUsageOpen} onOpenChange={setIsPlanUsageOpen}>
              <CollapsibleTrigger asChild>
                <CardHeader className="cursor-pointer hover:bg-gray-50 dark:hover:bg-slate-700/50 transition-colors rounded-t-lg">
                  <CardTitle className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <div className="p-2 rounded-lg bg-emerald-100 dark:bg-emerald-500/20">
                        <TrendingUp className="h-5 w-5 text-emerald-600 dark:text-emerald-400" />
                      </div>
                      <span className="text-lg">Plan Usage</span>
                    </div>
                    <ChevronDown className={cn(
                      "h-4 w-4 transition-transform",
                      isPlanUsageOpen ? "rotate-180" : ""
                    )} />
                  </CardTitle>
                </CardHeader>
              </CollapsibleTrigger>
              <CollapsibleContent>
                <CardContent className="pt-6">
                  <div className="space-y-6">
                    {/* Deductible Progress */}
                    <div className="space-y-3">
                      <div className="flex justify-between items-center">
                        <span className="font-medium">Deductible (Individual)</span>
                        <span className="text-sm text-gray-600 dark:text-slate-300">
                          ${profile?.planUsage?.deductible?.individual?.met?.toFixed(2) || '0.00'} / ${profile?.planUsage?.deductible?.individual?.amount?.toFixed(2) || '0.00'}
                        </span>
                      </div>
                      <Progress
                        value={profile?.planUsage?.deductible?.individual?.percentMet || 0}
                        className="w-full h-3"
                      />
                      <p className="text-sm text-gray-500 dark:text-slate-400">
                        {profile?.planUsage?.deductible?.individual?.percentMet?.toFixed(1) || '0.0'}% met
                      </p>
                    </div>

                    {/* Out-of-Pocket Max Progress */}
                    <div className="space-y-3">
                      <div className="flex justify-between items-center">
                        <span className="font-medium">Out-of-Pocket Max</span>
                        <span className="text-sm text-gray-600 dark:text-slate-300">
                          ${profile?.planUsage?.outOfPocketMax?.individual?.met?.toFixed(2) || '0.00'} / ${profile?.planUsage?.outOfPocketMax?.individual?.amount?.toFixed(2) || '0.00'}
                        </span>
                      </div>
                      <Progress
                        value={profile?.planUsage?.outOfPocketMax?.individual?.percentMet || 0}
                        className="w-full h-3"
                      />
                      <p className="text-sm text-gray-500 dark:text-slate-400">
                        {profile?.planUsage?.outOfPocketMax?.individual?.percentMet?.toFixed(1) || '0.0'}% met
                      </p>
                    </div>

                    {/* Optimize Costs Button */}
                    <div className="pt-4 border-t border-gray-200 dark:border-slate-700">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => {/* Placeholder */}}
                        className="w-full text-emerald-600 hover:text-emerald-700 dark:text-emerald-400 dark:hover:text-emerald-300 hover:bg-emerald-50 dark:hover:bg-emerald-500/10"
                      >
                        <DollarSign className="h-4 w-4 mr-2" />
                        Optimize my costs
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </CollapsibleContent>
            </Collapsible>
          </Card>

          {/* Claims Section */}
          <Card className="shadow-lg border-0 bg-white/80 dark:bg-slate-800/80 backdrop-blur-sm">
            <Collapsible open={isClaimsOpen} onOpenChange={setIsClaimsOpen}>
              <CollapsibleTrigger asChild>
                <CardHeader className="cursor-pointer hover:bg-gray-50 dark:hover:bg-slate-700/50 transition-colors rounded-t-lg">
                  <CardTitle className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <div className="p-2 rounded-lg bg-violet-100 dark:bg-violet-500/20">
                        <FileText className="h-5 w-5 text-violet-600 dark:text-violet-400" />
                      </div>
                      <div>
                        <span className="text-lg">Recent Claims</span>
                        <Badge variant="secondary" className="ml-2">
                          {recentClaims.length}
                        </Badge>
                      </div>
                    </div>
                    <ChevronDown className={cn(
                      "h-4 w-4 transition-transform",
                      isClaimsOpen ? "rotate-180" : ""
                    )} />
                  </CardTitle>
                </CardHeader>
              </CollapsibleTrigger>
              <CollapsibleContent>
                <CardContent className="pt-4">
                  {recentClaims.length === 0 ? (
                    <div className="text-center py-8">
                      <FileText className="h-12 w-12 text-gray-300 dark:text-gray-600 mx-auto mb-3" />
                      <p className="text-gray-500 dark:text-gray-400">
                        No recent claims found
                      </p>
                    </div>
                  ) : (
                    <div className="space-y-4">
                      {recentClaims.map((claim, index) => (
                        <div
                          key={`${claim.claimId}-${index}`}
                          className="p-4 rounded-xl bg-gradient-to-r from-violet-50 to-violet-100 dark:from-slate-700/50 dark:to-slate-600/50 border border-violet-200 dark:border-violet-500/30 space-y-3"
                        >
                          <div className="flex items-start justify-between">
                            <div className="flex-1">
                              <h3 className="font-semibold text-base mb-2">
                                {claim.procedureDescription}
                              </h3>
                              <p className="text-sm text-gray-600 dark:text-slate-300 mb-2">
                                {claim.providerName} • {claim.claimDate}
                              </p>
                              <div className="flex items-center gap-4 text-sm text-gray-500 dark:text-slate-400">
                                <span>Amount: ${claim.billedAmount}</span>
                                <span className={cn(
                                  "px-3 py-1 rounded-full text-xs font-medium",
                                  claim.claimStatus === 'Paid' ? 'bg-emerald-100 text-emerald-800 dark:bg-emerald-500/20 dark:text-emerald-300' :
                                  claim.claimStatus === 'Pending' ? 'bg-amber-100 text-amber-800 dark:bg-amber-500/20 dark:text-amber-300' :
                                  'bg-red-100 text-red-800 dark:bg-red-500/20 dark:text-red-300'
                                )}>
                                  {claim.claimStatus}
                                </span>
                              </div>
                            </div>
                          </div>
                          <div className="flex gap-3 pt-2">
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => handleClaimInquiry(claim.providerName, claim.claimDate)}
                              className="flex-1"
                            >
                              Ask about this claim
                            </Button>
                          </div>
                        </div>
                      ))}
                      <div className="mt-6 pt-4 border-t border-gray-200 dark:border-slate-700">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => {/* Placeholder */}}
                          className="w-full text-blue-600 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300 hover:bg-blue-50 dark:hover:bg-blue-500/10"
                        >
                          View All Claims
                        </Button>
                      </div>
                    </div>
                  )}
                </CardContent>
              </CollapsibleContent>
            </Collapsible>
          </Card>

          {/* Condition Management Section */}
          <Card className="shadow-lg border-0 bg-white/80 dark:bg-slate-800/80 backdrop-blur-sm lg:col-span-2 xl:col-span-1">
            <Collapsible open={isConditionMgmtOpen} onOpenChange={setIsConditionMgmtOpen}>
              <CollapsibleTrigger asChild>
                <CardHeader className="cursor-pointer hover:bg-gray-50 dark:hover:bg-slate-700/50 transition-colors rounded-t-lg">
                  <CardTitle className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <div className="p-2 rounded-lg bg-rose-100 dark:bg-rose-500/20">
                        <HeartHandshake className="h-5 w-5 text-rose-600 dark:text-rose-400" />
                      </div>
                      <span className="text-lg">Condition Management</span>
                    </div>
                    <ChevronDown className={cn(
                      "h-4 w-4 transition-transform",
                      isConditionMgmtOpen ? "rotate-180" : ""
                    )} />
                  </CardTitle>
                </CardHeader>
              </CollapsibleTrigger>
              <CollapsibleContent>
                <CardContent className="pt-4">
                  <div className="space-y-4">
                    <div className="p-4 rounded-xl bg-gradient-to-r from-purple-50 to-purple-100 dark:from-slate-700/50 dark:to-slate-600/50 border border-purple-200 dark:border-purple-500/30 space-y-3">
                      <div>
                        <h3 className="font-semibold text-base flex items-center gap-2 mb-2">
                          <Phone className="h-5 w-5 text-purple-600 dark:text-purple-400" />
                          Dedicated Nurse Line
                        </h3>
                        <p className="text-sm text-gray-600 dark:text-slate-300 mb-3">
                          Connect with a dedicated nurse for personalized support
                        </p>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => handleDedicatedNurseLine()}
                          className="border-purple-300 dark:border-purple-500 hover:bg-purple-50 dark:hover:bg-purple-500/10 text-purple-700 dark:text-purple-300"
                        >
                          Schedule Call Back
                        </Button>
                      </div>
                    </div>

                    <div className="p-4 rounded-xl bg-gradient-to-r from-blue-50 to-blue-100 dark:from-slate-700/50 dark:to-slate-600/50 border border-blue-200 dark:border-blue-500/30 space-y-3">
                      <div>
                        <h3 className="font-semibold text-base flex items-center gap-2 mb-2">
                          <Video className="h-5 w-5 text-blue-600 dark:text-blue-400" />
                          Telehealth
                        </h3>
                        <p className="text-sm text-gray-600 dark:text-slate-300 mb-3">
                          Schedule virtual appointments with specialists
                        </p>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => handleConditionTelehealth()}
                          className="border-blue-300 dark:border-blue-500 hover:bg-blue-50 dark:hover:bg-blue-500/10 text-blue-700 dark:text-blue-300"
                        >
                          Schedule Telehealth
                        </Button>
                      </div>
                    </div>

                    <div className="p-4 rounded-xl bg-gradient-to-r from-emerald-50 to-emerald-100 dark:from-slate-700/50 dark:to-slate-600/50 border border-emerald-200 dark:border-emerald-500/30 space-y-3">
                      <div>
                        <h3 className="font-semibold text-base flex items-center gap-2 mb-2">
                          <Activity className="h-5 w-5 text-emerald-600 dark:text-emerald-400" />
                          Symptom Logger
                        </h3>
                        <p className="text-sm text-gray-600 dark:text-slate-300 mb-3">
                          Track your symptoms and health patterns
                        </p>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => {/* Placeholder */}}
                          className="border-emerald-300 dark:border-emerald-500 hover:bg-emerald-50 dark:hover:bg-emerald-500/10 text-emerald-700 dark:text-emerald-300"
                        >
                          Open Logger
                        </Button>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </CollapsibleContent>
            </Collapsible>
          </Card>

          {/* Wellness and Prevention Section */}
          <Card className="shadow-lg border-0 bg-white/80 dark:bg-slate-800/80 backdrop-blur-sm lg:col-span-2 xl:col-span-1">
            <Collapsible open={isWellnessOpen} onOpenChange={setIsWellnessOpen}>
              <CollapsibleTrigger asChild>
                <CardHeader className="cursor-pointer hover:bg-gray-50 dark:hover:bg-slate-700/50 transition-colors rounded-t-lg">
                  <CardTitle className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <div className="p-2 rounded-lg bg-cyan-100 dark:bg-cyan-500/20">
                        <Shield className="h-5 w-5 text-cyan-600 dark:text-cyan-400" />
                      </div>
                      <span className="text-lg">Wellness and Prevention</span>
                    </div>
                    <ChevronDown className={cn(
                      "h-4 w-4 transition-transform",
                      isWellnessOpen ? "rotate-180" : ""
                    )} />
                  </CardTitle>
                </CardHeader>
              </CollapsibleTrigger>
              <CollapsibleContent>
                <CardContent className="pt-4">
                  <div className="space-y-4">
                    <div className="p-4 rounded-xl bg-gradient-to-r from-cyan-50 to-cyan-100 dark:from-slate-700/50 dark:to-slate-600/50 border border-cyan-200 dark:border-cyan-500/30 space-y-3">
                      <h3 className="font-semibold text-base mb-2">Personalized Exercise Plan</h3>
                      <p className="text-sm text-gray-600 dark:text-slate-300 mb-3">
                        Custom fitness plan based on your health profile
                      </p>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => {/* Placeholder */}}
                        className="border-cyan-300 dark:border-cyan-500 hover:bg-cyan-50 dark:hover:bg-cyan-500/10 text-cyan-700 dark:text-cyan-300"
                      >
                        View Plan
                      </Button>
                    </div>

                    <div className="p-4 rounded-xl bg-gradient-to-r from-cyan-50 to-cyan-100 dark:from-slate-700/50 dark:to-slate-600/50 border border-cyan-200 dark:border-cyan-500/30 space-y-3">
                      <h3 className="font-semibold text-base mb-2">Prevent Chronic Flu Reminders</h3>
                      <p className="text-sm text-gray-600 dark:text-slate-300 mb-3">
                        Stay up to date with preventive care reminders
                      </p>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => {/* Placeholder */}}
                        className="border-cyan-300 dark:border-cyan-500 hover:bg-cyan-50 dark:hover:bg-cyan-500/10 text-cyan-700 dark:text-cyan-300"
                      >
                        Set Reminders
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </CollapsibleContent>
            </Collapsible>
          </Card>

        </div>
      </div>

      {/* Fixed Bottom Chat Input */}
      <div className={cn("fixed bottom-0 right-0 bg-white/95 dark:bg-slate-900/95 backdrop-blur-sm border-t border-gray-200 dark:border-slate-700 p-4 z-10", inputViewLeft)}>
        <div className="container mx-auto max-w-4xl">
          <form
            onSubmit={handleChatSubmit}
            className="flex items-end gap-3 p-3 rounded-2xl bg-white dark:bg-slate-800 shadow-lg border border-gray-200 dark:border-slate-700"
            role="form"
            aria-label="Start new chat form"
          >
            <Textarea
              ref={textareaRef}
              placeholder="Ask me anything..."
              value={chatMessage}
              onChange={(e) => setChatMessage(e.target.value)}
              onKeyDown={(e) => {
                if (e.key === 'Enter' && !e.shiftKey) {
                  e.preventDefault();
                  handleChatSubmit();
                }
              }}
              className="flex-grow min-h-[44px] max-h-32 resize-none border-0 focus-visible:ring-0 focus-visible:ring-offset-0 text-base p-3 shadow-none bg-transparent"
              autoFocus={!isMobile}
              aria-label="Chat message input"
              maxLength={2000}
              rows={1}
            />
            <Button
              type="submit"
              size="icon"
              disabled={!chatMessage.trim() || isSending}
              className={cn(
                "h-11 w-11 shrink-0",
                "bg-blue-600 hover:bg-blue-700 dark:bg-blue-500 dark:hover:bg-blue-400",
                "transition-all duration-200"
              )}
              aria-label={isSending ? "Starting chat" : "Start new chat"}
            >
              {isSending ? (
                <Loader2 className="h-5 w-5 animate-spin" aria-hidden="true" />
              ) : (
                <ArrowUp className="h-5 w-5" aria-hidden="true" />
              )}
            </Button>
          </form>
          <p className="text-xs text-gray-500 dark:text-gray-400 text-center mt-2">
            Start a new conversation • Press Enter to send
          </p>
        </div>
      </div>
    </div>
  );
}
