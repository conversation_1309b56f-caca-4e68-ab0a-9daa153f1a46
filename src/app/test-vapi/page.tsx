"use client";

import { useState, useEffect } from 'react';
import { useChat } from '@/contexts/ChatContext';
import { getUserId, resetUserId } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { toast } from 'sonner';

export default function TestVapiPage() {
  const { userId, threads, currentThreadId } = useChat();
  const [redisStatus, setRedisStatus] = useState<'testing' | 'success' | 'error' | 'idle'>('idle');
  const [redisMessage, setRedisMessage] = useState('');
  const [pendingItems, setPendingItems] = useState<any[]>([]);
  const [syncStatus, setSyncStatus] = useState<'testing' | 'success' | 'error' | 'idle'>('idle');

  const testRedisConnection = async () => {
    setRedisStatus('testing');
    try {
      const response = await fetch('/api/test/redis');
      const data = await response.json();
      
      if (data.success) {
        setRedisStatus('success');
        setRedisMessage('Redis connection successful!');
        toast.success('Redis connection test passed');
      } else {
        setRedisStatus('error');
        setRedisMessage(data.error || 'Redis test failed');
        toast.error('Redis connection test failed');
      }
    } catch (error) {
      setRedisStatus('error');
      setRedisMessage('Network error testing Redis');
      toast.error('Network error testing Redis');
    }
  };

  const testSyncEndpoint = async () => {
    setSyncStatus('testing');
    try {
      const response = await fetch('/api/sync/pending');
      const data = await response.json();
      
      if (response.ok) {
        setSyncStatus('success');
        setPendingItems(data.items || []);
        toast.success(`Sync test passed - ${data.items?.length || 0} pending items`);
      } else {
        setSyncStatus('error');
        toast.error('Sync endpoint test failed');
      }
    } catch (error) {
      setSyncStatus('error');
      toast.error('Network error testing sync');
    }
  };

  const testAppointmentFunction = async () => {
    try {
      const testPayload = {
        message: {
          function: {
            arguments: {
              type: 'telehealth',
              provider: 'Dr. Test Provider',
              specialty: 'Test Specialty',
              date: '2025-01-20',
              time: '14:00'
            }
          }
        },
        call: {
          metadata: {
            userId: userId,
            threadId: currentThreadId || 'test_thread'
          }
        }
      };

      const response = await fetch('/api/vapi/schedule-appointment', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(testPayload)
      });

      const data = await response.json();
      if (data.success) {
        toast.success('Appointment function test passed');
      } else {
        toast.error('Appointment function test failed');
      }
    } catch (error) {
      toast.error('Error testing appointment function');
    }
  };

  const testMessageFunction = async () => {
    try {
      const testPayload = {
        message: {
          function: {
            arguments: {
              message: 'Test message from VAPI integration',
              type: 'regular'
            }
          }
        },
        call: {
          metadata: {
            userId: userId,
            threadId: currentThreadId || 'test_thread'
          }
        }
      };

      const response = await fetch('/api/vapi/send-message', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(testPayload)
      });

      const data = await response.json();
      if (data.success) {
        toast.success('Message function test passed');
      } else {
        toast.error('Message function test failed');
      }
    } catch (error) {
      toast.error('Error testing message function');
    }
  };

  const handleResetUserId = () => {
    resetUserId();
    window.location.reload();
  };

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="text-center">
        <h1 className="text-3xl font-bold">VAPI Integration Test Suite</h1>
        <p className="text-muted-foreground mt-2">
          Test all components of the VAPI integration system
        </p>
      </div>

      {/* User ID Status */}
      <Card>
        <CardHeader>
          <CardTitle>User ID Management</CardTitle>
          <CardDescription>Test user identification system</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <span>Current User ID:</span>
            <Badge variant="outline">{userId}</Badge>
          </div>
          <div className="flex items-center justify-between">
            <span>Current Thread ID:</span>
            <Badge variant="outline">{currentThreadId || 'None selected'}</Badge>
          </div>
          <div className="flex items-center justify-between">
            <span>Total Threads:</span>
            <Badge variant="outline">{threads.length}</Badge>
          </div>
          <Button onClick={handleResetUserId} variant="outline" size="sm">
            Reset User ID (for testing)
          </Button>
        </CardContent>
      </Card>

      {/* Redis Connection Test */}
      <Card>
        <CardHeader>
          <CardTitle>Redis Connection</CardTitle>
          <CardDescription>Test Redis database connectivity</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <span>Status:</span>
            <Badge variant={
              redisStatus === 'success' ? 'default' : 
              redisStatus === 'error' ? 'destructive' : 
              redisStatus === 'testing' ? 'secondary' : 'outline'
            }>
              {redisStatus === 'testing' ? 'Testing...' : 
               redisStatus === 'success' ? 'Connected' :
               redisStatus === 'error' ? 'Failed' : 'Not tested'}
            </Badge>
          </div>
          {redisMessage && (
            <div className="text-sm text-muted-foreground">
              {redisMessage}
            </div>
          )}
          <Button 
            onClick={testRedisConnection} 
            disabled={redisStatus === 'testing'}
            size="sm"
          >
            Test Redis Connection
          </Button>
        </CardContent>
      </Card>

      {/* Sync Endpoint Test */}
      <Card>
        <CardHeader>
          <CardTitle>Sync Endpoint</CardTitle>
          <CardDescription>Test pending items synchronization</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <span>Status:</span>
            <Badge variant={
              syncStatus === 'success' ? 'default' : 
              syncStatus === 'error' ? 'destructive' : 
              syncStatus === 'testing' ? 'secondary' : 'outline'
            }>
              {syncStatus === 'testing' ? 'Testing...' : 
               syncStatus === 'success' ? 'Working' :
               syncStatus === 'error' ? 'Failed' : 'Not tested'}
            </Badge>
          </div>
          <div className="flex items-center justify-between">
            <span>Pending Items:</span>
            <Badge variant="outline">{pendingItems.length}</Badge>
          </div>
          <Button 
            onClick={testSyncEndpoint} 
            disabled={syncStatus === 'testing'}
            size="sm"
          >
            Test Sync Endpoint
          </Button>
        </CardContent>
      </Card>

      {/* Function Tests */}
      <Card>
        <CardHeader>
          <CardTitle>VAPI Functions</CardTitle>
          <CardDescription>Test individual function endpoints</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Button onClick={testAppointmentFunction} variant="outline">
              Test Appointment Function
            </Button>
            <Button onClick={testMessageFunction} variant="outline">
              Test Message Function
            </Button>
          </div>
          <div className="text-sm text-muted-foreground">
            These tests simulate function calls from VAPI with your current user/thread IDs
          </div>
        </CardContent>
      </Card>

      {/* Pending Items Display */}
      {pendingItems.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Pending Items</CardTitle>
            <CardDescription>Items waiting to be synced</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {pendingItems.map((item, index) => (
                <div key={index} className="flex items-center justify-between p-2 border rounded">
                  <div>
                    <Badge variant="outline" className="mr-2">{item.type}</Badge>
                    <span className="text-sm">{item.id}</span>
                  </div>
                  <span className="text-xs text-muted-foreground">
                    {new Date(item.createdAt).toLocaleString()}
                  </span>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Instructions */}
      <Card>
        <CardHeader>
          <CardTitle>Next Steps</CardTitle>
          <CardDescription>Complete the integration setup</CardDescription>
        </CardHeader>
        <CardContent className="space-y-2 text-sm">
          <div>1. ✅ User ID system is working</div>
          <div>2. 🔧 Configure Redis credentials in .env.local</div>
          <div>3. 🔧 Set webhook URL in Vapi Dashboard: https://gennext.vercel.app/api/vapi/webhook</div>
          <div>4. 🧪 Test with actual voice calls</div>
          <div>5. 🗑️ Remove test components when satisfied</div>
        </CardContent>
      </Card>
    </div>
  );
}
