"use client";

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Phone, PhoneCall } from 'lucide-react';
import { toast } from 'sonner';
import { useSidebar } from '@/components/ui/sidebar';
import { getUserId } from '@/lib/utils';

export default function ClinicalOutreachPage() {
  const [phoneNumber, setPhoneNumber] = useState('');
  const [isCallActive, setIsCallActive] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [userId] = useState<string>(getUserId()); // Get or create user ID
  const { setOpen } = useSidebar();

  useEffect(() => {
    setOpen(false);
  }, [setOpen]);

  // Phone number validation
  const validatePhoneNumber = (phone: string): boolean => {
    // Basic phone number validation - accepts various formats
    const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/;
    const cleanPhone = phone.replace(/[\s\-\(\)\.]/g, '');
    return phoneRegex.test(cleanPhone) && cleanPhone.length >= 10;
  };

  const formatPhoneNumber = (phone: string): string => {
    // Clean and format phone number for E.164 format
    const cleanPhone = phone.replace(/[\s\-\(\)\.]/g, '');
    if (!cleanPhone.startsWith('+')) {
      return cleanPhone.startsWith('1') ? `+${cleanPhone}` : `+1${cleanPhone}`;
    }
    return cleanPhone;
  };

  const startOutboundCall = async () => {
    if (!phoneNumber.trim()) {
      toast.error("Please enter a phone number");
      return;
    }

    if (!validatePhoneNumber(phoneNumber)) {
      toast.error("Please enter a valid phone number");
      return;
    }

    setIsLoading(true);

    try {
      const formattedPhone = formatPhoneNumber(phoneNumber);

      // Call our backend API to initiate the outbound call
      toast.info("Initiating outbound call...");

      const response = await fetch('/api/outbound-call', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          phoneNumber: formattedPhone,
          userId: userId,
          assistantType: 'clinical-outreach'
        }),
      });

      const result = await response.json();

      if (response.ok) {
        toast.success(`Post-surgical knee surgery follow-up call initiated to ${formattedPhone}`);
        setIsCallActive(true);

        // Reset form after successful call
        setTimeout(() => {
          setIsCallActive(false);
          setPhoneNumber('');
        }, 30000); // Reset after 30 seconds
      } else {
        throw new Error(result.error || 'Failed to initiate call');
      }
      
    } catch (error) {
      console.error("Failed to start outbound call:", error);
      toast.error("Failed to initiate call. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="light-mode-override min-h-screen bg-white p-4">
      <div className="max-w-2xl mx-auto space-y-6">
        <div className="text-center space-y-2">
          <h1 className="text-3xl font-bold tracking-tight text-gray-900">Clinical Outreach Experience</h1>
          <p className="text-gray-600">
            Experience a Post-surgical knee surgery follow-up call with Virtual Assistant. Simulating a Clinical Outreach call that a Member would recieve following a Knee Replacement Surgery.
          </p>
        </div>

        <Card className="bg-white border-gray-200 shadow-sm">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-gray-900">
              <Phone className="h-5 w-5 text-gray-700" />
              Recieve A Phone Call From The Virtual Assistant
            </CardTitle>
            <CardDescription className="text-gray-600">
              Enter your phone number to initiate a post-surgical knee surgery follow-up call with Liz, a Virtual Assistant Assistant.
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="phone" className="text-gray-900 font-medium">Your Phone Number</Label>
              <Input
                id="phone"
                type="tel"
                placeholder="(*************"
                value={phoneNumber}
                onChange={(e) => setPhoneNumber(e.target.value)}
                className="text-lg bg-white border-gray-300 text-gray-900"
              />
              <p className="text-sm text-gray-600">
                Enter phone number in any format (e.g., 234567890, (*************, ************)
              </p>
            </div>

            <Button 
              onClick={startOutboundCall}
              disabled={isLoading || isCallActive}
              className="w-full dark:bg-black! dark:text-white!"
              size="lg"
            >
              {isLoading ? (
                "Initiating Call..."
              ) : (
                <>
                  <PhoneCall className="mr-2 h-4 w-4 dark:text-white!" />
                  Start Knee Surgery Follow-up Call
                </>
              )}
            </Button>

            {isCallActive && (
              <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
                <p className="text-green-800 font-medium">Call in progress...</p>
                <p className="text-green-600 text-sm">
                  Liz is conducting the post-surgical knee surgery follow-up call.
                </p>
              </div>
            )}
          </CardContent>
        </Card>

        <Card className="bg-white border-gray-200 shadow-sm">
          <CardHeader>
            <CardTitle className="text-gray-900">About Post-Surgical Knee Surgery Follow-up</CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <p className="text-sm text-gray-600">
              The Virtual Assistant assistant Liz will conduct a professional post-surgical follow-up call to:
            </p>
            <ul className="text-sm text-gray-600 space-y-1 ml-4">
              <li>• Check on knee surgery recovery progress and pain levels</li>
              <li>• Highlight Post Care benefits available through their health plan</li>
              <li>• Provide information about 24/7 Nurse Line availability</li>
              <li>• Offer Well Being package with mental wellness resources</li>
              <li>• Educate about recovery expectations and Physical Therapy importance</li>
              <li>• Help schedule Physical Therapy with in-network providers</li>
              <li>• Address medication questions and offer nurse callbacks</li>
              <li>• Remind about call transcript availability in Sydney App</li>
            </ul>
            <div className="mt-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
              <p className="text-blue-800 text-sm font-medium">
                Focus: Post-operative care education to prevent ER visits and ensure proper recovery
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
