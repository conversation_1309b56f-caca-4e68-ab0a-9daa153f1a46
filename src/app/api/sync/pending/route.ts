import { NextRequest, NextResponse } from 'next/server';
import { redis, getCurrentUserId, isRedisAvailable } from '@/lib/redis';

export async function GET(request: NextRequest) {
  try {
    // Check if Redis is available
    if (!isRedisAvailable() || !redis) {
      console.log('SYNC: Redis not available, returning empty array');
      return NextResponse.json({ items: [] }); // Return empty array when Redis is not available
    }

    // Get userId from query parameter or fallback to getCurrentUserId
    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId') || getCurrentUserId();
    console.log('SYNC: Getting pending items for userId:', userId);

    // Get all pending item IDs for user
    const pendingIds = await redis.smembers(`user_pending_items:${userId}`) || [];
    console.log('SYNC: Found pending IDs:', pendingIds);

    if (pendingIds.length === 0) {
      console.log('SYNC: No pending items found');
      return NextResponse.json({ items: [] });
    }

    // Fetch all pending items
    const items = [];
    for (const id of pendingIds) {
      const item = await redis.get(`pending_item:${id}`);
      if (item) {
        // Handle both string and object responses from Redis
        let parsedItem;
        if (typeof item === 'string') {
          parsedItem = JSON.parse(item);
        } else {
          parsedItem = item; // Already an object
        }
        console.log('SYNC: Retrieved item:', id, 'type:', parsedItem.type);
        items.push(parsedItem);
      } else {
        console.log('SYNC: Item not found for ID:', id);
      }
    }

    console.log('SYNC: Returning', items.length, 'items');
    return NextResponse.json({ items });

  } catch (error) {
    console.error('Sync error:', error);
    return NextResponse.json({ error: 'Sync failed' }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    // Check if Redis is available
    if (!isRedisAvailable() || !redis) {
      return NextResponse.json({ success: true }); // Return success when Redis is not available
    }

    const { itemIds, userId: providedUserId } = await request.json();
    const userId = providedUserId || getCurrentUserId();

    // Mark items as processed by removing them
    for (const id of itemIds) {
      await redis.srem(`user_pending_items:${userId}`, id);
      await redis.del(`pending_item:${id}`);
    }

    return NextResponse.json({ success: true });

  } catch (error) {
    console.error('Mark delivered error:', error);
    return NextResponse.json({ error: 'Failed to mark as delivered' }, { status: 500 });
  }
}
