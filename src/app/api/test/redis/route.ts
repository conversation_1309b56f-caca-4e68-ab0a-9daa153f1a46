import { NextRequest, NextResponse } from 'next/server';
import { redis, isRedisAvailable } from '@/lib/redis';

export async function GET(request: NextRequest) {
  try {
    // Check if Redis is available
    if (!isRedisAvailable() || !redis) {
      return NextResponse.json({
        success: false,
        error: 'Redis not configured',
        details: 'Redis credentials are not properly configured. Please set UPSTASH_REDIS_REST_URL and UPSTASH_REDIS_REST_TOKEN in your environment variables.'
      }, { status: 503 });
    }

    // Test Redis connection
    const testKey = 'test_connection';
    const testValue = 'Redis is working!';

    // Set a test value
    await redis.set(testKey, testValue);

    // Get the test value
    const result = await redis.get(testKey);

    // Clean up
    await redis.del(testKey);

    return NextResponse.json({
      success: true,
      message: 'Redis connection successful',
      testResult: result
    });

  } catch (error) {
    console.error('Redis test error:', error);
    return NextResponse.json({
      success: false,
      error: 'Redis connection failed',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
