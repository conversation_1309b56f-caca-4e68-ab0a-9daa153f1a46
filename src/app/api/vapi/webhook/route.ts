import { NextRequest, NextResponse } from 'next/server';
import { redis, generateItemId, isRedisAvailable } from '@/lib/redis';

export async function POST(request: NextRequest) {
  try {
    const payload = await request.json();
    console.log('WEBHOOK: Received payload');
    console.log('WEBHOOK: Event type:', payload.message?.type);
    console.log('Shape of data:', JSON.stringify(payload, null, 2));

    // Only process end-of-call events
    if (payload.message?.type !== 'end-of-call-report') {
      console.log('WEBHOOK: Ignoring non-ended event, status:', payload.status);
      return NextResponse.json({ received: true });
    }

    console.log('WEBHOOK: Processing call completion for call:', payload.id);
    await handleCallCompletion(payload);
    return NextResponse.json({ received: true });

  } catch (error) {
    console.error('Webhook error:', error);
    return NextResponse.json({ error: 'Webhook processing failed' }, { status: 500 });
  }
}

async function handleCallCompletion(payload: any) {
  const { id: callId, messages, analysis, startedAt, endedAt, endedReason } = payload.message;
  console.log('WEBHOOK HANDLER: Processing call completion for:', callId);

  // Extract user identification from the correct path
  const { userId, threadId } = payload.message?.assistant?.metadata || {};
  console.log('WEBHOOK HANDLER: Extracted metadata - userId:', userId, 'threadId:', threadId);

  if (!userId) {
    console.warn('WEBHOOK HANDLER: No userId in webhook metadata, skipping transcript storage');
    console.log('WEBHOOK HANDLER: Available metadata paths:', {
      'payload.message?.assistant?.metadata': payload.message?.assistant?.metadata,
      'payload.assistant?.metadata': payload.assistant?.metadata,
      'payload.metadata': payload.assistant?.metadata
    });
    return;
  }

  // Only store in Redis if it's available
  if (!isRedisAvailable() || !redis) {
    console.warn('WEBHOOK HANDLER: Redis not available, transcript data not stored');
    return;
  }

  // Convert Vapi messages to transcript format
  console.log('WEBHOOK HANDLER: Processing messages:', messages?.length || 0, 'messages');
  const transcriptText = messages?.map((msg: any) => {
    const role = msg.role === 'assistant' ? 'AI' : 'User';
    return `${role}: ${msg.message}`;
  }).join('\n') || '';
  console.log('WEBHOOK HANDLER: Generated transcript text length:', transcriptText.length);

  // Handle recording URLs (may need adjustment based on actual Vapi response)
  const recordingUrls = {
    mono: payload.message?.recordingUrl || '',
    stereo: payload.message?.stereoRecordingUrl || ''
  };
  console.log('WEBHOOK HANDLER: Recording URLs:', recordingUrls);

  // Create transcript data in existing format
  const transcriptId = generateItemId('transcript');
  const transcriptData = {
    id: transcriptId,
    type: 'transcript',
    userId,
    threadId,
    data: {
      id: payload.message?.timestamp,
      summary: payload.message?.analysis?.summary || '',
      transcript: payload.message?.transcript,
      startedAt: payload.message?.startedAt,
      endedAt: payload.message?.endedAt,
      endedReason: payload.message?.endedReason || 'call-ended',
      recordingUrls: recordingUrls
    },
    createdAt: new Date().toISOString()
  };

  console.log('WEBHOOK HANDLER: Created transcript data:', transcriptData);

  // Store in Redis
  console.log('WEBHOOK HANDLER: Storing transcript in Redis with key:', `pending_item:${transcriptId}`);
  await redis.setex(`pending_item:${transcriptId}`, 86400, JSON.stringify(transcriptData));

  // Add to user's pending list
  console.log('WEBHOOK HANDLER: Adding to user pending list:', `user_pending_items:${userId}`);
  await redis.sadd(`user_pending_items:${userId}`, transcriptId);

  console.log(`WEBHOOK HANDLER: Successfully stored transcript for call ${callId}, user ${userId}`);
}

export async function OPTIONS(_req: Request) {
    return new Response(null, {
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Methods': 'POST, OPTIONS',
            'Access-Control-Allow-Headers': 'Content-Type, Authorization',
        },
    });
}