@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
  --color-sidebar-ring: var(--sidebar-ring);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar: var(--sidebar);
  --color-chart-5: var(--chart-5);
  --color-chart-4: var(--chart-4);
  --color-chart-3: var(--chart-3);
  --color-chart-2: var(--chart-2);
  --color-chart-1: var(--chart-1);
  --color-ring: var(--ring);
  --color-input: var(--input);
  --color-border: var(--border);
  --color-destructive: var(--destructive);
  --color-accent-foreground: var(--accent-foreground);
  --color-accent: var(--accent);
  --color-muted-foreground: var(--muted-foreground);
  --color-muted: var(--muted);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-secondary: var(--secondary);
  --color-primary-foreground: var(--primary-foreground);
  --color-primary: var(--primary);
  --color-popover-foreground: var(--popover-foreground);
  --color-popover: var(--popover);
  --color-card-foreground: var(--card-foreground);
  --color-card: var(--card);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
  --animate-shine: shine var(--duration) infinite linear;
  @keyframes shine {
  0% {
    background-position: 0% 0%;
    }
  50% {
    background-position: 100% 100%;
    }
  to {
    background-position: 0% 0%;
    }
  }
}

:root {
  --radius: 0.625rem;
  --background: oklch(0.98 0 0);
  --foreground: oklch(0.145 0 0);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.145 0 0);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.145 0 0);
  --primary: oklch(0.205 0 0);
  --primary-foreground: oklch(0.985 0 0);
  --secondary: oklch(0.97 0 0);
  --secondary-foreground: oklch(0.205 0 0);
  --muted: oklch(0.97 0 0);
  --muted-foreground: oklch(0.556 0 0);
  --accent: oklch(0.97 0 0);
  --accent-foreground: oklch(0.205 0 0);
  --destructive: oklch(0.577 0.245 27.325);
  --border: oklch(0.922 0 0);
  --input: oklch(0.922 0 0);
  --ring: oklch(0.708 0 0);
  --chart-1: oklch(0.646 0.222 41.116);
  --chart-2: oklch(0.6 0.118 184.704);
  --chart-3: oklch(0.398 0.07 227.392);
  --chart-4: oklch(0.828 0.189 84.429);
  --chart-5: oklch(0.769 0.188 70.08);
  --sidebar: oklch(0.985 0 0);
  --sidebar-foreground: oklch(0.145 0 0);
  --sidebar-primary: oklch(0.205 0 0);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.97 0 0);
  --sidebar-accent-foreground: oklch(0.205 0 0);
  --sidebar-border: oklch(0.922 0 0);
  --sidebar-ring: oklch(0.708 0 0);

  #bento-grid-item {
    @apply bg-black rounded-3xl items-start;
    text-align: left !important;
    p {
      @apply text-left text-white shadow-2xl;
    }
    h3 {
      @apply text-left text-white shadow-2xl;
    }
    svg {
      @apply text-white shadow-2xl;
    }
    a {
      @apply text-[#38D7FF];
      svg {
        @apply text-[#38D7FF];
      }
    }
  }
  #VC_Response {
    @apply rounded-3xl p-4 bg-transparent shadow-xl;
    border: 2px solid transparent;
    background: #fdfdfd; 
    #VC_Card {
     
    }
    
  }

  #VC_h1 {
    @apply bg-clip-text text-transparent font-medium text-6xl;
    background-image: linear-gradient(to bottom, #C78FFF, #38D7FF);
  }
  h2 {
    @apply text-[#333] text-base font-medium;
  }
}

.dark {
  --background: oklch(0.3655 0.0615 282.93);
  --foreground: oklch(0.985 0 0);
  --card: oklch(0.205 0 0);
  --card-foreground: oklch(0.985 0 0);
  --popover: oklch(0.205 0 0);
  --popover-foreground: oklch(0.985 0 0);
  --primary: oklch(0.922 0 0);
  --primary-foreground: oklch(0.205 0 0);
  --secondary: oklch(0.269 0 0);
  --secondary-foreground: oklch(0.985 0 0);
  --muted: oklch(0.269 0 0);
  --muted-foreground: oklch(82.656% 0.00009 271.152);
  --accent: oklch(0.269 0 0);
  --accent-foreground: oklch(0.985 0 0);
  --destructive: oklch(0.704 0.191 22.216);
  --border: oklch(1 0 0 / 10%);
  --input: oklch(1 0 0 / 15%);
  --ring: oklch(0.556 0 0);
  --chart-1: oklch(0.488 0.243 264.376);
  --chart-2: oklch(0.696 0.17 162.48);
  --chart-3: oklch(0.769 0.188 70.08);
  --chart-4: oklch(0.627 0.265 303.9);
  --chart-5: oklch(0.645 0.246 16.439);
  --sidebar: oklch(0.205 0 0);
  --sidebar-foreground: oklch(0.985 0 0);
  --sidebar-primary: oklch(0.488 0.243 264.376);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.269 0 0);
  --sidebar-accent-foreground: oklch(0.985 0 0);
  --sidebar-border: oklch(1 0 0 / 10%);
  --sidebar-ring: oklch(0.556 0 0);
  /* body {
      background: linear-gradient(to bottom, #3A3A5E, #282844, #0e152d) !important;
    }
    .bg-background {
      background: linear-gradient(to bottom, #3A3A5E, #282844, #0e152d) !important;
    } */
    .bg-genui {
      background: linear-gradient(to bottom, #3A3A5E, #282844, #0e152d) !important;
    }
    #VC_h1 {
    @apply bg-clip-text text-transparent font-medium text-6xl;
    background-image: linear-gradient(to bottom, #C78FFF, #38D7FF);
  }
  h2 {
    @apply text-[#38D7FF] text-base font-medium;
  }
  #VC_Input_Landing {
    @apply rounded-3xl px-4 pt-2 pb-4 bg-zinc-800 shadow-xl;
    /* border: 2px solid transparent;
    background: linear-gradient(oklch(0.29 0.04 276.05), oklch(0.29 0.04 276.05)) padding-box,
                linear-gradient(to bottom, #C78FFF, #38D7FF) border-box; */
    /* box-shadow: 0 0 15px rgba(199, 143, 255, 0.3), 0 0 30px rgba(56, 215, 255, 0.2); */
    textarea {
      @apply p-2 m-0;
      background-color: transparent !important;
    }
  }
  #VC_Response {
    @apply rounded-3xl p-4 bg-transparent shadow-xl;
    border: 2px solid transparent;
    background: linear-gradient(oklch(0.29 0.04 276.05), oklch(0.29 0.04 276.05)) padding-box,
                linear-gradient(to bottom, #C78FFF, #38D7FF) border-box; 
    #VC_Card {
     
    }
    
  }
  --card: oklch(0.37 0.06 282.95);
  #VC_Input {
    @apply rounded-3xl px-4 pt-2 pb-4 bg-zinc-800 shadow-xl;
    border: 2px solid transparent;
    background: linear-gradient(oklch(0.29 0.04 276.05), oklch(0.29 0.04 276.05)) padding-box,
                linear-gradient(to bottom, #C78FFF, #38D7FF) border-box; 
     box-shadow: 0 0 15px rgba(199, 143, 255, 0.3), 0 0 30px rgba(56, 215, 255, 0.2); 
    textarea {
      @apply p-2 m-0;
      background-color: transparent !important;
    }
  }
  #send-button {
    @apply bg-[#38d7ff] hover:bg-[#19839d] disabled:bg-[#38d7ff] rounded-full ;
    svg {
      @apply text-black;
    }
  }
  .bento-grid-item {
    @apply bg-transparent rounded-3xl items-start;
    text-align: left !important;
    p {
      @apply text-left text-white shadow-2xl;
    }
    h3 {
      @apply text-left text-[#38D7FF] shadow-2xl;
    }
    svg {
      @apply text-[#38D7FF] shadow-2xl;
    }
    a {
      @apply text-[#38D7FF];
      svg {
        @apply text-[#38D7FF];
      }
    }
  }
}

@utility no-scrollbar {
  @apply [scrollbar-width:none] [&::-webkit-scrollbar]:hidden;
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }

  ul {
    @apply pb-4;
  }

  p {
    @apply py-2;
  }

  :root {
    --sidebar: oklch(0.985 0 0);
    --sidebar-foreground: oklch(0.145 0 0);
    --sidebar-primary: oklch(0.205 0 0);
    --sidebar-primary-foreground: oklch(0.985 0 0);
    --sidebar-accent: oklch(0.97 0 0);
    --sidebar-accent-foreground: oklch(0.205 0 0);
    --sidebar-border: oklch(0.922 0 0);
    --sidebar-ring: oklch(0.708 0 0);
  }

  .dark {
    /* --sidebar: oklch(8 10.29 282.34); */
    --sidebar-foreground: oklch(0.985 0 0);
    --sidebar-primary: oklch(0.488 0.243 264.376);
    --sidebar-primary-foreground: oklch(0.985 0 0);
    --sidebar-accent: oklch(0.269 0 0);
    --sidebar-accent-foreground: oklch(0.985 0 0);
    --sidebar-border: oklch(1 0 0 / 10%);
    --sidebar-ring: oklch(0.439 0 0);
    .bg-sidebar {
      background-color: #3a3a5e !important;
    }
  }
}

/* ==========================================================================
   RESPONSIVE DESIGN UTILITIES
   ========================================================================== */

@layer utilities {
  /* Enhanced touch targets for mobile accessibility */
  .touch-target {
    @apply min-h-[44px] min-w-[44px];
  }

  /* Responsive text sizing */
  .text-responsive {
    @apply text-sm md:text-base;
  }

  .text-responsive-lg {
    @apply text-base md:text-lg;
  }

  .text-responsive-xl {
    @apply text-lg md:text-xl;
  }

  /* Responsive spacing utilities */
  .spacing-responsive {
    @apply p-3 md:p-4 lg:p-6;
  }

  .spacing-responsive-sm {
    @apply p-2 md:p-3 lg:p-4;
  }

  /* Mobile-first responsive containers */
  .container-responsive {
    @apply w-full max-w-sm md:max-w-2xl lg:max-w-4xl xl:max-w-6xl mx-auto;
  }

  /* Enhanced focus states for accessibility */
  .focus-enhanced {
    @apply focus:outline-none focus:ring-2 focus:ring-primary/20 focus:ring-offset-2;
  }

  /* Smooth scrolling for better UX */
  .scroll-smooth-enhanced {
    scroll-behavior: smooth;
    -webkit-overflow-scrolling: touch; /* iOS momentum scrolling */
  }

  /* Mobile drawer animations */
  .drawer-slide-in {
    animation: slideInFromLeft 0.3s ease-out;
  }

  .drawer-slide-out {
    animation: slideOutToLeft 0.3s ease-in;
  }

  /* Performance optimizations */
  .gpu-accelerated {
    transform: translateZ(0);
    will-change: transform;
  }

  /* Mobile-optimized input styling */
  .input-mobile-optimized {
    @apply text-base; /* Prevents zoom on iOS */
    font-size: max(16px, 1rem); /* Ensures 16px minimum for iOS */
  }

  /* Responsive grid utilities */
  .grid-responsive {
    @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 md:gap-6;
  }

  .grid-responsive-auto {
    @apply grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4;
  }

  /* Enhanced button states for mobile */
  .button-mobile-enhanced {
    @apply active:scale-95 transition-transform duration-150;
  }

  /* Responsive message bubbles */
  .message-bubble-responsive {
    @apply max-w-[85%] md:max-w-[75%] lg:max-w-[70%];
  }

  /* Safe area handling for mobile devices */
  .safe-area-top {
    padding-top: env(safe-area-inset-top);
  }

  .safe-area-bottom {
    padding-bottom: env(safe-area-inset-bottom);
  }

  .safe-area-left {
    padding-left: env(safe-area-inset-left);
  }

  .safe-area-right {
    padding-right: env(safe-area-inset-right);
  }
}

/* ==========================================================================
   RESPONSIVE ANIMATIONS
   ========================================================================== */

@keyframes slideInFromLeft {
  from {
    transform: translateX(-100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideOutToLeft {
  from {
    transform: translateX(0);
    opacity: 1;
  }
  to {
    transform: translateX(-100%);
    opacity: 0;
  }
}

@keyframes fadeInUp {
  from {
    transform: translateY(20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes scaleIn {
  from {
    transform: scale(0.9);
    opacity: 0;
  }
  to {
    transform: scale(1);
    opacity: 1;
  }
}

/* ==========================================================================
   MOBILE-SPECIFIC OPTIMIZATIONS
   ========================================================================== */

/* Reduce motion for users who prefer it */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .border {
    @apply border-2;
  }

  .focus-enhanced {
    @apply focus:ring-4;
  }
}

/* Dark mode optimizations */
@media (prefers-color-scheme: dark) {
  /* Enhanced scrollbar styling for dark mode */
  ::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }

  ::-webkit-scrollbar-track {
    background: hsl(var(--muted));
  }

  ::-webkit-scrollbar-thumb {
    background: hsl(var(--muted-foreground) / 0.3);
    border-radius: 4px;
  }

  ::-webkit-scrollbar-thumb:hover {
    background: hsl(var(--muted-foreground) / 0.5);
  }
}

/* ==========================================================================
   RESPONSIVE BREAKPOINT DOCUMENTATION
   ========================================================================== */

/*
  BREAKPOINT SYSTEM:
  - Mobile: < 768px (default)
  - Tablet: 768px - 1023px (md:)
  - Desktop: 1024px - 1279px (lg:)
  - Large Desktop: 1280px+ (xl:)
  - Extra Large: 1536px+ (2xl:)

  TOUCH TARGET GUIDELINES:
  - Minimum 44px x 44px for mobile touch targets
  - 8px minimum spacing between interactive elements
  - Prefer larger touch targets on mobile (48px+ recommended)

  RESPONSIVE DESIGN PRINCIPLES:
  - Mobile-first approach
  - Progressive enhancement
  - Accessible by default
  - Performance optimized
  - Touch-friendly interactions
*/

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}