'use client';

import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { ThumbsUp, ThumbsDown } from 'lucide-react';
import { toast } from 'sonner';
import { cn } from '@/lib/utils';

interface MessageFeedbackProps {
  messageId: string;
  threadId: string;
  className?: string;
}

export function MessageFeedback({ messageId, threadId, className }: MessageFeedbackProps) {
  const [feedbackGiven, setFeedbackGiven] = useState<'up' | 'down' | null>(() => {
    // Check if feedback has already been given for this message by reading from localStorage
    try {
      const negativeFeedback = localStorage.getItem('negative_feedback');
      if (negativeFeedback) {
        const feedbackData = JSON.parse(negativeFeedback);
        if (Array.isArray(feedbackData)) {
          const existingFeedback = feedbackData.find(
            (feedback: any) => feedback.id === `${threadId}-${messageId}`
          );
          if (existingFeedback) {
            return 'down';
          }
        }
      }
    } catch (error) {
      console.error('Error reading feedback from localStorage:', error);
    }
    
    return null;
  });

  const handlePositiveFeedback = () => {
    if (feedbackGiven) return;
    
    setFeedbackGiven('up');
    toast.success('Thanks for your feedback!');
  };

  const handleNegativeFeedback = () => {
    if (feedbackGiven) return;
    
    setFeedbackGiven('down');
    
    // Store negative feedback in localStorage
    try {
      const negativeFeedback = localStorage.getItem('negative_feedback');
      const feedbackData = negativeFeedback ? JSON.parse(negativeFeedback) : [];
      
      const newFeedback = {
        id: `${threadId}-${messageId}`,
        threadId,
        messageId,
        timestamp: Date.now(),
        type: 'negative'
      };
      
      feedbackData.push(newFeedback);
      localStorage.setItem('negative_feedback', JSON.stringify(feedbackData));
      
      toast.success('Thanks for your feedback, we will look into this');
    } catch (error) {
      console.error('Error storing negative feedback:', error);
      toast.error('Failed to store feedback');
    }
  };

  return (
    <div className={cn('flex items-center gap-2', className)}>
      <Button
        variant="ghost"
        size="sm"
        onClick={handlePositiveFeedback}
        disabled={feedbackGiven !== null}
        className={cn(
          'h-6 w-6 p-0 hover:bg-green-50 dark:hover:bg-green-950',
          feedbackGiven === 'up' && 'bg-green-100 dark:bg-green-900 text-green-700 dark:text-green-300'
        )}
        aria-label="Give positive feedback"
      >
        <ThumbsUp className="h-3 w-3" />
      </Button>
      
      <Button
        variant="ghost"
        size="sm"
        onClick={handleNegativeFeedback}
        disabled={feedbackGiven !== null}
        className={cn(
          'h-6 w-6 p-0 hover:bg-red-50 dark:hover:bg-red-950',
          feedbackGiven === 'down' && 'bg-red-100 dark:bg-red-900 text-red-700 dark:text-red-300'
        )}
        aria-label="Give negative feedback"
      >
        <ThumbsDown className="h-3 w-3" />
      </Button>
    </div>
  );
}