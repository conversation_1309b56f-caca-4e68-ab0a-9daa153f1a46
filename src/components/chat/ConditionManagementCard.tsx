import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import { 
  HeartHandshake, 
  Video, 
  Pill, 
  Users, ScanHeart 
} from "lucide-react";

interface ConditionManagementCardProps {
  onScheduleCallBack?: () => void;
  onScheduleTelehealth?: () => void;
  onMedicationHelp?: () => void;
  onOpenCareCanvas?: () => void;
}

const ConditionManagementCard: React.FC<ConditionManagementCardProps> = ({
  onScheduleCallBack,
  onScheduleTelehealth,
  onMedicationHelp,
  onOpenCareCanvas
}) => {
  const features = [
    {
      icon: HeartHandshake,
      title: "Care management",
      description: "A nurse care manager can help you stay on track with care planning, appointments, and lifestyle changes. \n \n If you’re not sure where to start, talking with a nurse care manager is a great first step.",
      color: "text-blue-600",
      buttonConfig: {
        label: "Connect with a nurse",
        onClick: onScheduleCallBack,
        className: "border-blue-300 dark:border-blue-500 hover:bg-blue-50 dark:hover:bg-blue-500/10 text-blue-700 dark:text-blue-300"
      }
    },
    {
      icon: Video,
      title: "Virtual visits",
      description: "See GI specialists and other doctors from home, on your schedule.",
      color: "text-purple-600",
      buttonConfig: {
        label: "Schedule a virtual visit",
        onClick: onScheduleTelehealth,
        className: "border-purple-300 dark:border-purple-500 hover:bg-purple-50 dark:hover:bg-purple-500/10 text-purple-700 dark:text-purple-300"
      }
    },
    {
      icon: Pill,
      title: "Easy medication management",
      description: "Set up refills, get delivery reminders, and check for drug interactions.",
      color: "text-orange-600",
      buttonConfig: {
        label: "Check refills and reminders",
        onClick: onMedicationHelp,
        className: "border-orange-300 dark:border-orange-500 hover:bg-orange-50 dark:hover:bg-orange-500/10 text-orange-700 dark:text-orange-300"
      }
    },
    {
      icon: ScanHeart,
      title: "Personalized plan insights",
      description: "Stay on top of your health with a quick view of your plan, care team, and time-sensitive tasks.",
      color: "text-teal-600",
      buttonConfig: {
        label: "Open care canvas",
        onClick: onOpenCareCanvas,
        className: "border-teal-300 dark:border-teal-500 hover:bg-teal-50 dark:hover:bg-teal-500/10 text-teal-700 dark:text-teal-300"
      }
    }
  ];

  return (
    <Card className="w-full max-w-none sm:max-w-2xl py-8">
      {/* <CardHeader className="pb-4">
        <div className="flex items-center gap-3">
          <CardTitle className="text-xl font-semibold">
            Personalized Condition Managements
          </CardTitle>
          <Badge variant="secondary" className="bg-blue-50 text-blue-700 dark:bg-blue-900 dark:text-blue-200">
            Crohn's
          </Badge>
        </div>
      </CardHeader> */}
      
      <CardContent className="space-y-6 pt-0">
        {features.map((feature, index) => {
          const IconComponent = feature.icon;
          return (
            <div key={index}>
              <div className="flex items-start gap-4">
                <div className={`flex-shrink-0 p-2 rounded-lg bg-gray-50 dark:bg-gray-800 ${feature.color}`}>
                  <IconComponent size={20} />
                </div>
                <div className="flex-1 space-y-2">
                  <h4 className="font-semibold text-gray-900 dark:text-white">
                    {feature.title} 
                  </h4>
                  <p className="text-sm text-gray-600 dark:text-gray-300 leading-relaxed">
                    {feature.description.split('\n').map((line, index) => <span key={index}>{line}<br /></span>)}
                  </p>
                  {feature.buttonConfig && feature.buttonConfig.onClick && (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={feature.buttonConfig.onClick}
                      className={`mt-3 text-xs ${feature.buttonConfig.className}`}
                    >
                      {feature.buttonConfig.label}
                    </Button>
                  )}
                </div>
              </div>
              {index < features.length - 1 && (
                <Separator className="mt-6" />
              )}
            </div>
          );
        })}
      </CardContent>
    </Card>
  );
};

export default ConditionManagementCard;