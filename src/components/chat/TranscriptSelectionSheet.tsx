'use client';

import { useChat } from '@/contexts/ChatContext';
import { useTranscripts } from '@/hooks/use-transcripts';
import { Button } from '@/components/ui/button';
import { Sheet, <PERSON><PERSON><PERSON>ontent, She<PERSON><PERSON>eader, Sheet<PERSON>itle, SheetDes<PERSON>, SheetFooter } from '@/components/ui/sheet';
import { ScrollArea } from '@/components/ui/scroll-area';
import { TranscriptListItem } from '@/components/transcripts/TranscriptListItem';
import { Transcript } from '@/types';
import { useRouter } from 'next/navigation';
import { RefreshCw } from 'lucide-react';
import { useState } from 'react';
import { toast } from 'sonner';

interface TranscriptSelectionSheetProps {
  isOpen: boolean;
  onOpenChange: (isOpen: boolean) => void;
}

export function TranscriptSelectionSheet({ isOpen, onOpenChange }: TranscriptSelectionSheetProps) {
  const { transcripts } = useTranscripts();
  const { selectedTranscriptIds, addTranscript, removeTranscript, syncPendingItems } = useChat();
  const router = useRouter();
  const [isSyncing, setIsSyncing] = useState(false);

  const handleTranscriptSelect = (checked: boolean, transcriptId: string) => {
    if (checked) {
      addTranscript(transcriptId);
    } else {
      removeTranscript(transcriptId);
    }
  };

  const handleManualSync = async () => {
    setIsSyncing(true);
    try {
      await syncPendingItems();
      toast.success('Sync completed');
    } catch (error) {
      console.error('Manual sync failed:', error);
      toast.error('Sync failed');
    } finally {
      setIsSyncing(false);
    }
  };

  return (
    <Sheet open={isOpen} onOpenChange={onOpenChange}>
      <SheetContent className="w-full sm:w-[540px] flex flex-col dark:bg-[#262a3f]">
        <SheetHeader>
          <div className="flex items-center justify-between">
            <div>
              <SheetTitle>Attach Transcripts</SheetTitle>
              <SheetDescription className='text-sm text-muted-foreground'>
                Select call transcripts to provide additional context to the conversation.
              </SheetDescription>
            </div>
            <Button
              variant="outline"
              size="sm"
              onClick={handleManualSync}
              disabled={isSyncing}
              className="flex items-center gap-2"
            >
              <RefreshCw className={`h-4 w-4 ${isSyncing ? 'animate-spin' : ''}`} />
              {isSyncing ? 'Syncing...' : 'Sync'}
            </Button>
          </div>
        </SheetHeader>
        <ScrollArea className="flex-grow my-0 h-[60vh]">
          <div className="space-y-4 px-4">
            {transcripts.length > 0 ? (
              transcripts.map((transcript: Transcript) => (
                <TranscriptListItem
                  key={transcript.id}
                  transcript={transcript}
                  selectable={true}
                  selected={selectedTranscriptIds.includes(transcript.id)}
                  onSelectionChange={handleTranscriptSelect}
                />
              ))
            ) : (
              <p className="text-muted-foreground text-center py-8">No transcripts available.</p>
            )}
          </div>
        </ScrollArea>
        <SheetFooter>
          <Button onClick={() => onOpenChange(false)}>Done</Button>
        </SheetFooter>
      </SheetContent>
    </Sheet>
  );
} 