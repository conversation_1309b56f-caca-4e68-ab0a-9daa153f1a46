"use client";

import { getUserId, resetUserId } from '@/lib/utils';
import { useChat } from '@/contexts/ChatContext';
import { useState, useEffect } from 'react';

export function UserIdTest() {
  const { userId } = useChat();
  const [localUserId, setLocalUserId] = useState<string>('');

  useEffect(() => {
    setLocalUserId(getUserId());
  }, []);

  const handleReset = () => {
    const newId = resetUserId();
    setLocalUserId(newId);
    window.location.reload(); // Reload to see new ID in context
  };

  return (
    <div style={{ 
      position: 'fixed', 
      top: 10, 
      right: 10, 
      background: 'black', 
      color: 'white', 
      padding: '10px',
      borderRadius: '5px',
      fontSize: '12px',
      zIndex: 9999
    }}>
      <div><strong>User ID Test</strong></div>
      <div>Context ID: {userId}</div>
      <div>Local ID: {localUserId}</div>
      <button 
        onClick={handleReset}
        style={{
          marginTop: '5px',
          padding: '2px 5px',
          fontSize: '10px',
          cursor: 'pointer'
        }}
      >
        Reset User ID
      </button>
    </div>
  );
}
