export interface ChatResponsePart {
  type: 'text' | 'function_call' | 'function_result' | 'error';
  content?: string; // For 'text' type
  functionName?: string; // For 'function_call' and 'function_result'
  functionArgs?: any; // For 'function_call'
  functionData?: any; // For 'function_result'
  errorMessage?: string; // For 'error'
}

export interface Suggestion {
  text: string;
}

export interface ForYouItem {
  id: string;
  title: string;
  initialMessage: string;
}

export interface ShowConditionManagementData {
  condition: string;
  forYouItems: ForYouItem[];
}

export interface Transcript {
  id: string;
  summary: string;
  transcript: string;
  startedAt: string;
  endedAt: string;
  endedReason: string;
  recordingUrls: {
    mono: string;
    stereo: string;
  };
}

export interface Message {
  id: string;
  sender: "user" | "ai" | "system" | "voice_assistant";
  timestamp: number;
  // For user messages, content will be a string
  // For AI messages, content will be an array of ChatResponsePart
  // For system messages (like voice call events), content will be a string
  // For voice_assistant messages, content will be a string
  content: string | ChatResponsePart[];
  // Optional metadata for system messages
  messageType?: "voice_call_start" | "voice_call_end" | "regular";
}

export interface Thread {
  id: string;
  name: string;
  messages: Message[];
  createdAt: number;
  lastModifiedAt: number;
  initialMessageSent?: boolean;
}

export interface PharmacyOrderStatus {
  medicationName: string;
  dosage: string;
  pharmacyName: string;
  pharmacyPhone: string;
  costEstimate: string;
  currentStatus: 'Received' | 'In Progress' | 'Out for Delivery' | 'Delivered';
}

export interface Provider {
  id: string;
  name: string;
  distance: string; // e.g., "3.2 mi"
  cost: string; // e.g., "$62.50" (representing "What you pay")
  rating: number; // e.g., 4.7
  reviewsCount: number; // e.g., 2391
  specialty: string;
  address: string; // This field was previously 'location'
  phone: string;
}

export interface InsurancePlan {
  planName: string;
  planType: string;
  groupType: string;
}

export interface Dependent {
  dependentMemberId: string;
  dependentRelationship: string;
}

export interface PrimaryCareProvider {
  providerId: string;
  name: string;
  specialty: string;
  practice: string;
  location: string;
  phone: string;
  assignedDate: string;
}

export interface CareTeamMember {
  providerId: string;
  name: string;
  specialty: string;
  role: string;
  practice: string;
  location: string;
  phone: string;
  assignedDate: string;
  managedConditions: string[];
  isPrimary: boolean;
}

export interface MedicalCondition {
  conditionId: string;
  conditionName: string;
  diagnosisDate: string;
  status: string;
  isPrimary: boolean;
  notes: string;
}

export interface Prescription {
  prescriptionId: string;
  memberId: string;
  memberName: string;
  medicationName: string;
  brandName: string | null;
  dosage: string;
  frequency: string;
  route: string;
  conditionIds: string[];
  notes: string;
  lastFillDate?: string;
  daysSupply?: number;
  refillsRemaining?: number;
  isEnrolledInAutoRefill?: boolean;
  isEligibleForAdvancedHomeDelivery?: boolean;
  isEnrolledInAdvancedHomeDelivery?: boolean;
  tier?: string;
  priorAuthorization?: {
    status: string;
    expiryDate?: string;
  };
}

export interface HealthRecord {
  recordId: string;
  memberId: string;
  recordType: string;
  recordDate: string;
  providerName: string;
  description: string;
  summary: string;
  detailedReportLink: string | null;
  resultsSummary?: string; // For lab results
}

export interface Claim {
  claimId: string;
  memberId: string;
  claimDate: string;
  providerName: string;
  providerType: string;
  procedureDescription: string;
  diagnosisCode: string;
  billedAmount: number;
  allowedAmount: number;
  paidAmount: number;
  patientResponsibility: number;
  claimStatus: string;
  conditionIds: string[];
}

export interface Deductible {
  amount: number;
  met: number;
  remaining: number;
  percentMet: number;
}

export interface BenefitUtilizationItem {
  used: number;
  covered: string | number;
  copay?: number;
  coinsurance?: number;
  remaining?: number;
}

export interface SpendingByCategoryItem {
  category: string;
  amount: number;
  percentOfTotal: number;
}

export interface RecentActivity {
  date: string;
  type: string;
  description: string;
  amount: number;
  appliedToDeductible: boolean;
}

export interface PlanUsage {
  year: number;
  asOf: string;
  deductible: {
    individual: Deductible;
    family: Deductible;
  };
  outOfPocketMax: {
    individual: Deductible;
    family: Deductible;
  };
  benefitUtilization: {
    specialistVisits: BenefitUtilizationItem;
    primaryCareVisits: BenefitUtilizationItem;
    prescriptionDrugs: {
      tier1: BenefitUtilizationItem;
      tier2: BenefitUtilizationItem;
      specialtyDrugs: BenefitUtilizationItem;
    };
    preventiveCare: BenefitUtilizationItem;
    mentalHealthVisits: BenefitUtilizationItem;
  };
  spendingByCategory: {
    totalSpent: number;
    byCategory: SpendingByCategoryItem[];
  };
  recentActivity: RecentActivity[];
}

export interface PharmacyPreferences {
  defaultRetailPharmacy: {
    name: string;
    address: string;
  };
  homeDelivery: {
    address: string;
  };
}

export interface PaymentMethod {
  type: string;
  last4: string;
  expiry: string;
  isDefault: boolean;
}

export interface ProfileData {
  memberId: string;
  memberName: string;
  dateOfBirth: string;
  gender: string;
  location: string;
  insurancePlan: InsurancePlan;
  familyStatus: string;
  dependents: Dependent[];
  primaryCareProvider: PrimaryCareProvider;
  careTeam: CareTeamMember[];
  medicalConditions: MedicalCondition[];
  prescriptions: Prescription[];
  healthRecords: HealthRecord[];
  claims: Claim[];
  planUsage: PlanUsage;
  pharmacyPreferences: PharmacyPreferences;
  paymentMethods: PaymentMethod[];
}