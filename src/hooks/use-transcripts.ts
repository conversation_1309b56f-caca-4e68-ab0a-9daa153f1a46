'use client';

import { useState, useEffect } from 'react';
import { Transcript } from '@/types';
import defaultTranscriptsData from '@/app/data/call_transcript.json';

const defaultTranscripts: Transcript[] = defaultTranscriptsData as Transcript[];

export function useTranscripts() {
  const [transcripts, setTranscripts] = useState<Transcript[]>([]);

  // Function to load transcripts from localStorage
  const loadTranscripts = () => {
    try {
      const storedTranscripts = localStorage.getItem('transcripts');
      if (storedTranscripts) {
        const parsedTranscripts = JSON.parse(storedTranscripts) as Transcript[];
        // Sort transcripts by startedAt date, most recent first
        const sortedTranscripts = parsedTranscripts.sort((a, b) => new Date(b.startedAt).getTime() - new Date(a.startedAt).getTime());
        setTranscripts(sortedTranscripts);
      } else {
        // Sort default transcripts as well
        const sortedDefaultTranscripts = [...defaultTranscripts].sort((a, b) => new Date(b.startedAt).getTime() - new Date(a.startedAt).getTime());
        localStorage.setItem('transcripts', JSON.stringify(sortedDefaultTranscripts));
        setTranscripts(sortedDefaultTranscripts);
      }
    } catch (error) {
      console.error('Failed to load transcripts from local storage:', error);
      // Fallback to sorted default transcripts if local storage fails
      const sortedDefaultTranscripts = [...defaultTranscripts].sort((a, b) => new Date(b.startedAt).getTime() - new Date(a.startedAt).getTime());
      setTranscripts(sortedDefaultTranscripts);
    }
  };

  // Load transcripts from localStorage on mount
  useEffect(() => {
    loadTranscripts();
  }, []);

  // Listen for custom events to reload transcripts
  useEffect(() => {
    const handleTranscriptAdded = () => {
      loadTranscripts();
    };

    window.addEventListener('transcriptAdded', handleTranscriptAdded);
    return () => {
      window.removeEventListener('transcriptAdded', handleTranscriptAdded);
    };
  }, []);

  return { transcripts };
}